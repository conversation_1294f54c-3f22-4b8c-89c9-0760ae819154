<?php
/**
 * Created by PhpStorm.
 * User: 庞俊超<<EMAIL>>
 * Date: 2018/5/15
 * Time: 15:13
 */
class Service_Data_AssistantNotice {

    private $_objDaoAssistantNotice;

    const ALL_FIELDS='id,assistantUid,type,content,status,detailType,sourceId,createTime,updateTime,deleted,extData,personUid';

    //定义通知状态  status
    const NOTICE_READ_NO = 0;//未读
    const NOTICE_READ_OK = 1;//已读

    const DELETED_OK = 1;
    const DELETED_NO = 0;

    //type
//    const TYPE_IN_CLASS  = 1;//进班
//    const TYPE_OUT_CLASS = 2;//退课
    const TYPE_NOTICE    = 3;//公告
    const TYPE_CHECK     = 4;//质检

    const TYPE_11 = 11; //报名
    const TYPE_12 = 12; //转入
    const TYPE_13 = 13; //转出
    const TYPE_14 = 14; //退课
    const TYPE_15 = 15; //续报
    const TYPE_16 = 16; //退续报

    const TYPE_17 = 17; //云学堂通知
    const TYPE_18 = 18; //调小班通知
    const TYPE_19 = 19; //课件审核通知
    const TYPE_20 = 20; //挽单通知

    const TYPE_21 = 21; //隔季续报
    const TYPE_22 = 22; //隔季退续报
    const TYPE_23 = 23; //触达任务通知
    const TYPE_Device_Notify = 24; //老师设备通知

    const TYPE_30 = 30; //提醒

    public static $typeMap = array(
        self::TYPE_NOTICE       => '公告',
        self::TYPE_CHECK        => '质检',

        self::TYPE_11           => '报名',
        self::TYPE_12           => '转入',
        self::TYPE_13           => '转出',
        self::TYPE_14           => '退课',

        self::TYPE_15           => '续报',
        self::TYPE_16           => '退续报',

        self::TYPE_17           => '培训',
        self::TYPE_18           => '调小班',

        self::TYPE_19           => '课件审核',
        self::TYPE_20           => '挽单',

        self::TYPE_21            => '隔季续报',
        self::TYPE_22            => '隔季退续报',
        self::TYPE_23            => '触达任务通知',
        self::TYPE_Device_Notify => '设备通知',
    );

    //类型hover显示文本
    public static $typeHoverText = [
        self::TYPE_15 => '续报通知仅作为学员状态变更提醒，不作为数据统计的依据',
    ];

    //detail_type
    //进班
    const TYPE_IN_CLASS_SERVICE  = 1;//客服调班进班
    const TYPE_IN_CLASS_SALESMAN = 2;//销售转换进班
    const TYPE_IN_CLASS_INCLASS  = 3;//开课后报名
    const TYPE_IN_CLASS_NEW      = 4;//学员新报名
    //离班
    const TYPE_OUT_CLASS_SERVICE = 1;//客服调班离班
    const TYPE_OUT_CLASS_DELETED = 2;//学员退课

    //质检
    const TYPE_VOICE_CHECK_SERVICE = 1;//录音
    const TYPE_INTERVIEW_CHECK_SERVICE = 2;//访谈记录
    const TYPE_WECHAT_CHECK_SERVICE = 3;//微信
    const TYPE_EXCELLENT_VOICE_CHECK_SERVICE = 4;//优秀录音
    const TYPE_EXCELLENT_PIC_CHECK_SERVICE = 5;//优秀截图
    const TYPE_WECHAT_CHECK_SERVICE_New = 6;//新版微信

    //各类通知的展示文案
    public static $typeInClass = array(
        self::TYPE_IN_CLASS_SERVICE     => '%s 客服调班加入 %s',
        self::TYPE_IN_CLASS_SALESMAN    => '%s 销售转化加入 %s',
        self::TYPE_IN_CLASS_INCLASS     => '%s 开课后报名加入 %s',
        self::TYPE_IN_CLASS_NEW         => '%s 新报名加入 %s',
    );
    public static $typeOutClass = array(
        self::TYPE_OUT_CLASS_SERVICE    => '%s 客服调班离开 %s',
        self::TYPE_OUT_CLASS_DELETED    => '%s 退课离开 %s',
    );
    public static $typeNotice = array(
        self::TYPE_NOTICE   =>  '您有一条新的公告：%s',
    );

    const TYPE_19_CHECKSTATUS_PASS = 2;//通过
    const TYPE_19_CHECKSTATUS_REJECT = 3;//驳回
    public static $jiangyiCheckStatusNameMap = [
        self::TYPE_19_CHECKSTATUS_PASS => '通过',
        self::TYPE_19_CHECKSTATUS_REJECT => '驳回',
    ];

    //质检的通知文案
    public static $typeCheck = array(
        self::TYPE_VOICE_CHECK_SERVICE              =>  '您的组员 %s 有一条录音违规，请在3天内点击确认，逾期将自动确认违规',
        self::TYPE_INTERVIEW_CHECK_SERVICE          =>  '您的组员 %s 有一条访谈记录违规，请在3天内点击确认，逾期将自动确认违规',
        self::TYPE_WECHAT_CHECK_SERVICE             =>  '您的组员 %s 有一条微信违规，请在3天内点击确认，逾期将自动确认违规',
        self::TYPE_EXCELLENT_VOICE_CHECK_SERVICE    =>  '您的组员 %s 有一条优秀录音未通过，请在3天内点击确认，逾期将自动确认未通过',
        self::TYPE_EXCELLENT_PIC_CHECK_SERVICE      =>  '您的组员 %s 有一条优秀截图未通过，请在3天内点击确认，逾期将自动确认未通过',
        self::TYPE_WECHAT_CHECK_SERVICE_New         =>  '您的组员 %s 有一条微信违规，请在3天内点击确认，逾期将自动确认未通过',
    );

    // notice.extData.directPage
    const NOTICE_DIRECT_PAGE_REFUND = "refund"; // 跳转到 退费挽单 页面
    const NOTICE_DIRECT_PAGE_DETAIL = "detail"; // 跳转到 维系详情 页面

    public function __construct(){
        $this->_objDaoAssistantNotice = new Dao_AssistantNotice();
    }

    /**
     * 新增记录
     * @param array $arrFields
     * @param array $arrConds
     * @return bool
     */
    public function insertRecords($arrFields = array()) {

        if (empty($arrFields)) {
            return false;
        }

        $ret = $this->_objDaoAssistantNotice->insertRecords($arrFields);

        if ($ret === false) {
            Bd_Log::warning("Error:[insertRecords], Detail:[conds:]" . json_encode($arrFields));
            return false;
        }

        return true;
    }

    /**
     * 新增记录多条记录
     * @param array $arrFields
     * @param array $values
     * @return bool|integer
     */
    public function multiInsert($arrFields = array(), $values = array()) {

        if (empty($arrFields) || empty($values)) {
            return false;
        }

        $ret = $this->_objDaoAssistantNotice->multiInsert($arrFields, $values);

        if ($ret === false) {
            Bd_Log::warning("Error:[multiInsert], Detail:[conds:]" . json_encode(['arrFields'=>$arrFields, 'values'=>$values]));
            return false;
        }

        return $ret;
    }

    /**
     * 条件更新
     * @param array $arrFields
     * @param array $arrConds
     * @return bool
     */
    public function updateRecords($arrFields = array(), $arrConds = array()) {

        if(empty($arrFields) || empty($arrConds)){
            return false;
        }

        $ret = $this->_objDaoAssistantNotice->updateByConds($arrConds, $arrFields);

        if ($ret === false) {
            Bd_Log::warning("Error:[updateByConds], Detail:[conds:]" . json_encode($arrFields));
            return false;
        }

        return true;
    }

    /**
     * 条件查询记录
     * @param $arrConds
     * @param array $arrFields
     * @param string $order
     * @param string $by
     * @param int $offset
     * @param int $limit
     * @return array|bool|false
     */
    public function getListByConds($arrConds,$arrFields = array(),$order = '',$by = '',$offset = 0,$limit = 0){
        if(empty($arrFields)){
            $arrFields = explode(',',self::ALL_FIELDS);
        }

        $orderBy = '';
        if($order != ''){
            $orderBy .= 'order by '. $order . ' ';
            $orderBy .= ($by == 'desc') ? 'desc' : 'asc';
        }
        if($offset >= 0 && $limit > 0 ){
            $orderBy .= " limit $offset,$limit";
        }else if($limit > 0){
            $orderBy .= " limit $limit";
        }
        $arrAppends = ($orderBy != '')? array($orderBy) : null;
        $ret = $this->_objDaoAssistantNotice->getListByConds($arrConds,$arrFields,NULL,$arrAppends);

        if(false === $ret){
            Bd_Log::warning("Error:[getListByConds], Detail:[conds:]" . json_encode($arrConds));
            return false;
        }

        return $ret;
    }

    /**
     * @param null $conds
     * 获取条数
     * @return false|int
     */
    public function getCount($conds = null)
    {
        $cnt = $this->_objDaoAssistantNotice->getCntByConds($conds);
        return $cnt;
    }

    /**
     * 对未读通知信息做聚合
     * @param array $noticeList
     * @return array $ret
     */
    public function unReadNoticeAggregate($noticeList){
        $ret = [];
        if (!is_array($noticeList) || empty($noticeList)) {
            return [];
        }
        //筛选出需要聚合通知 self::TYPE_11 报名 self::TYPE_18 调小班
        $aggregateTypeKeyArr = [self::TYPE_11 => self::TYPE_11, self::TYPE_18 => self::TYPE_18];
        $typeCourseGroupData = [];
        foreach ($noticeList as $i => $val){
            $courseId = $val['extData']['params']['courseId'];
            if (isset($aggregateTypeKeyArr[$val['type']])) {
                $typeCourseGroupData[$val['type']][$courseId][] = $val;
            } else {
                $ret[] = $val;
            }
        }
        //不存在需要聚合通知
        if (count($typeCourseGroupData) == 0) {
            return $ret;
        }
        //对需要聚合通知进行处理
        foreach ($aggregateTypeKeyArr as $type) {
            if (!isset($typeCourseGroupData[$type])) {
                continue;
            }
            foreach ($typeCourseGroupData[$type] as $cId => $list) {
                $nCount = count($list);
                $list[0]['isAggregate'] = 0;
                $list[0]['aggregateNum'] = 0;
                if ($nCount > 1) {
                    $list[0]['isAggregate'] = 1;
                    $list[0]['aggregateNum'] = $nCount;
                }
                $ret[] = $list[0];
            }
        }
        return $ret;
    }
}