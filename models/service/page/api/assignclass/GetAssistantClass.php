<?php
/**
 * @file GetAssistantClass.php
 * <AUTHOR>
 * @date 2019/2/15
 * @brief 排班接口-根据课程获取辅导老师小班对应关系
 */
class Service_Page_Api_Assignclass_GetAssistantClass {
    private $objDsAssistantNewTeacherCourse = null;
    private $objRedis = null;

    public function __construct() {
        $this->objDsAssistantNewTeacherCourse = new Service_Data_AssistantNewTeacherCourse();
        $this->objRedis         = Common_Redis::getInstance(Common_Redis::ZYB_CODIS_ZHIBO);
    }

    public function execute($arrInput) {
        $arrOutput = [
            'data' => [],
        ];



        if (! $this->checkParams($arrInput)) {
            Bd_Log::notice('Info:[params error] Detail:[arrInput: ' . json_encode($arrInput) . ']');
            return $arrOutput;
        }

        $courseId   = $arrInput['courseId'];

        $KEY_PIR = "GetAssistantClass_ASSISTANTDESK_API_";
        $KEY = $KEY_PIR.$courseId;
        $res = $this->objRedis ->get($KEY);
        if (!empty($res)){
            return json_decode($res,true);
        }else{
            $arrOutput['data'] = $this->getList($courseId);
            
            // 课中和助教收到nmq后通过该接口拿最新的辅导老师，因此去掉缓存 0910
//            $res = $this->objRedis ->setex($KEY,1800,json_encode($arrOutput));
            Bd_Log::notice('Info:[add GetAssistantClass REDISE] Detail:['.$courseId.': ' . json_encode($arrOutput) . ']');
        }
        //为空获取lpc数据
        if (empty($arrOutput['data'])) {
            //检查是否直播切换白名单课程
            if (Api_Mercury::CheckHitWithCourse(Api_Mercury::KEY_ASSISTANTDESK_INTERFACE_MIGRATION_GRAY, $courseId)) {
                Bd_Log::notice("hit " . Api_Mercury::KEY_ASSISTANTDESK_INTERFACE_MIGRATION_GRAY);
                $checkIsLiveCourse = Api_Tower::checkIsLiveCourse($courseId);
                $checkIsLiveCourseOld  = Api_Insight::checkIsLiveCourse($courseId);

                if ($checkIsLiveCourseOld != $checkIsLiveCourse && intval($courseId) > 2880000) {
                    Bd_log::warning("checkIsLiveCourse should be " . $checkIsLiveCourseOld . " but is " . $checkIsLiveCourse . " courseId is " . $courseId);
                    Laxin_Util_DingtalkRobot::sendDingtalk(
                        "checkIsLiveCourse should be " . $checkIsLiveCourseOld . " but is " . $checkIsLiveCourse . " courseId is " . $courseId, 
                        false, 
                        [], 
                        "81605e158ffea4e183d61e41af74aecdca60a07d62d4b1bbec8b9e0ee06fbe29"
                    );
                    $checkIsLiveCourse = $checkIsLiveCourseOld;
                }
            } else {
                $checkIsLiveCourse  = Api_Insight::checkIsLiveCourse($courseId);
            }
            if ($checkIsLiveCourse) {
                //获取lpc分班结果
                $lpcResult = Api_Sc::getClassByLpc($courseId);
                if (!empty($lpcResult)) {
                    $arrOutput['data']  = array_map('intval', $lpcResult);
                }
            }
        }

        return $arrOutput;
    }

    /**
     * 参数校验
     * @param $arrInput
     * @return bool
     */
    private function checkParams($arrInput) {
        if ($arrInput['courseId'] <=0) {
            return false;
        }
        return true;
    }

    /**
     * 获取classId-assistantUid关系列表
     * @param $courseId
     * @return array
     */
    private function getList($courseId) {
        $result = [];
        $arrConds = [
            'courseId' => $courseId,
            'deleted' => Service_Data_AssistantNewTeacherCourse::DELETED_NO,
        ];
        $arrFields = ['assistantUid', 'classId'];
        $list = $this->objDsAssistantNewTeacherCourse->getListByConds($arrConds, $arrFields);
        if (false === $list) {
            Bd_Log::warning("Error:[get list error],Detail:[courseId:{$courseId}]");
            return $result;
        }
        if (empty($list)) {
            Bd_Log::notice("Info:[no record] Detail:[courseId: {$courseId}]");
            return $result;
        }

        foreach ($list as $item) {
            if ($item['assistantUid'] !== Service_Data_AssignClass_AssignService::VIRTUAL_ASSISTANT_UID) {
                $result[$item['classId']] = $item['assistantUid'];
            }
        }
        if (empty($result)) {
            Bd_Log::notice("Info:[all record is virtual assistantUid] Detail:[courseId: {$courseId}]");
        }

        return $result;
    }
}