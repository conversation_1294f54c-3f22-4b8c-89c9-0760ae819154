<?php
/**
 * @file WhetherAssignClass.php
 * <AUTHOR>
 * @date 2019/2/15
 * @brief 排班接口-课程是否需要分班
 */
class Service_Page_Api_Assignclass_WhetherAssignClass {
    private $objDsAssistantNewCourse = null;
    private $objRedis = null;

    public function __construct() {
        $this->objDsAssistantNewCourse = new Service_Data_AssistantNewCourse();
        $this->objRedis         = Common_Redis::getInstance(Common_Redis::ZYB_CODIS_ZHIBO);
    }

    public function execute($arrInput) {
        $arrOutput = [
            'result' => false,
        ];

        if (! $this->checkParams($arrInput)) {
            Bd_Log::notice('Info:[params error] Detail:[arrInput: ' . json_encode($arrInput) . ']');
            return $arrOutput;
        }
        $courseId   = $arrInput['courseId'];

        $KEY_PIR = "WhetherAssignClass_ASSISTANTDESK_API_";
        $KEY = $KEY_PIR.$courseId;
//        $res = $this->objRedis->get($KEY);
        if (!empty($res)){
            return json_decode($res,true);
        }else{

            $courseType = $this->getCourseType($arrInput['courseId']);
            if (false === $courseType) {
                return $arrOutput;
            }

            // 0 短训班，2 长期班课
            $needType = [0, 2];
            if (in_array($courseType, $needType)) {
                $arrOutput['result'] = true;
            } else {
                Bd_Log::notice("Info:[this courseType doesn't assign class] Detail:[courseId: {$arrInput['courseId']}]");
            }
            //检查是否LPC课程
            if (!$arrOutput['result']) {
                //检查是否直播切换白名单课程
                $checkIsLiveCourse = false;
                if (Api_Mercury::CheckHitWithCourse(Api_Mercury::KEY_ASSISTANTDESK_INTERFACE_MIGRATION_GRAY, $courseId)) {
                    Bd_Log::notice("hit " . Api_Mercury::KEY_ASSISTANTDESK_INTERFACE_MIGRATION_GRAY);
                    $checkIsLiveCourse = Api_Tower::checkIsLiveCourse($courseId);
                    $checkIsLiveCourseOld  = Api_Insight::checkIsLiveCourse($courseId);

                    if ($checkIsLiveCourseOld != $checkIsLiveCourse && intval($courseId) > 2880000) {
                        Bd_log::warning("checkIsLiveCourse should be " . $checkIsLiveCourseOld . " but is " . $checkIsLiveCourse . " courseId is " . $courseId);
                        Laxin_Util_DingtalkRobot::sendDingtalk(
                            "checkIsLiveCourse should be " . $checkIsLiveCourseOld . " but is " . $checkIsLiveCourse . " courseId is " . $courseId, 
                            false, 
                            [], 
                            "81605e158ffea4e183d61e41af74aecdca60a07d62d4b1bbec8b9e0ee06fbe29"
                        );
                        $checkIsLiveCourse = $checkIsLiveCourseOld;
                    }
                    if ($checkIsLiveCourse) {
                        $arrOutput['result']    = $checkIsLiveCourse;
                    }
                } else {
                    $checkIsLiveCourse  = Api_Insight::checkIsLiveCourse($courseId);
                    if ($checkIsLiveCourse) {
                        //白名单课程判断是否LPC
                        $isLpcCourse            = Api_Insight::checkIsValidCourse($courseId);
                        $arrOutput['result']    = $isLpcCourse;
                    }
                }
            }
            $res = $this->objRedis ->setex($KEY,1800,json_encode($arrOutput));
            Bd_Log::notice('Info:[add WhetherAssignClass REDISE] Detail:['.$arrInput['courseId'].': ' . json_encode($arrOutput) . ']');
        }

        return $arrOutput;
    }

    /**
     * 参数校验
     * @param $arrInput
     * @return bool
     */
    private function checkParams($arrInput) {
        if ($arrInput['courseId'] <= 0) {
            return false;
        }
        return true;
    }

    /**
     *
     * @param $courseId
     * @return bool|int
     */
    private function getCourseType($courseId) {
        $result = false;
        if (empty($courseId)) {
            return $result;
        }
        $arrConds = array(
            'courseId' => $courseId,
            'state' => Service_Data_AssistantNewCourse::STATE_ALLOWED,
            'deleted' => Service_Data_AssistantNewCourse::DELETED_NO
        );
        $list = $this->objDsAssistantNewCourse->getRowByConds($arrConds, ['type', 'pullNewDuty']);
        if (false === $list) {
            Bd_Log::warning("Error:[get course type error] Detail:[courseId: {$courseId}]");
            return $result;
        }
        if (empty($list)) {
            Bd_Log::notice("Info:[no course] Detail:[courseId: {$courseId}]");
            return -1;
        }

        //打加油课标签的,返回给课中false,防止学生小班接口流量穿透
        if(($list['pullNewDuty'] & Service_Data_AssistantNewCourse::PULL_NEW_DUTY_BIT_2286) > 0){
            return $result;
        }

        return $list['type'];
    }

}