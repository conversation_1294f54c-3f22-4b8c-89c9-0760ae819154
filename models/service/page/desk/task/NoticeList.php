<?php
/**
 * Created by PhpStorm.
 * User: 庞俊超<<EMAIL>>
 * Date: 2018/5/17
 * Time: 16:31
 * 辅导老师 获取14天的通知接口
 */
class Service_Page_Desk_Task_NoticeList{

    private $_objDsAssistantNotice;
    private $_objDsAssistantNewCourse;

    public static $_type = [
        self::TYPE_GET_NEW_NOTICE => '>',
        self::TYPE_GET_OLD_NOTICE => '<',
    ];

    public static $_status = [
        self::TYPE_GET_NEW_NOTICE => Service_Data_AssistantNotice::NOTICE_READ_NO,
        self::TYPE_GET_OLD_NOTICE => Service_Data_AssistantNotice::NOTICE_READ_OK,
    ];
    //定义通知获取方式
    const TYPE_GET_NEW_NOTICE = 1;//获取新消息
    const TYPE_GET_OLD_NOTICE = 2;//获取历史消息

    const NOTICE_PAGE_SIZE = 30;//通知分页条数

    public function __construct(){
        $this->_objDsAssistantNotice     = new Service_Data_AssistantNotice();
        $this->_objDsAssistantNewCourse  = new Service_Data_AssistantNewCourse();
    }

    public function execute($arrInput){

        $personUid    = $arrInput['personUid'];
        $assistantUid = $arrInput['assistantUid'];
        $id           = $arrInput['id'];
        $type         = $arrInput['type'];
        $noticeType   = intval($arrInput['noticeType']);     //默认为私人+业务所有通知类型

        //type=1获取新消息；type=2获取历史消息，type为2时一定搭配id一起使用，获取历史消息时需要分页，每页30条
        //id为0时，type一定为1 ，表示默认获取【全部类型】的所有未读消息，此时不需要分页
        //点击查看全部消息时,其实是获取的已读消息，type=2,id=lastNoticeId，因为页面的第一步操作已经获取全部的未读消息了，此时拿到已读消息，前端append到一个全部消息列表
        //firstNoticeId  是按id倒序排序之后的第一个id
        //lastNoticeId   是按id倒序排序之后的最后一个id
        //查看最新消息时，type=1,id为上次请求返回的firstNoticeId
        //查看历史消息时，type=2,id为上次请求返回的lastNoticeId

        $arrOutput = array();
        $arrOutput['list'] = [];
        $arrOutput['firstNoticeId'] = 0;
        $arrOutput['lastNoticeId'] = 0;

        //非辅导老师系统用户，$personUid小于等于0，直接返回空数组，不抛异常 2019-11-19 单洪峰
        if (0 >= $personUid) {
            return $arrOutput;
        }

        if(!is_int($id) || 0 > $id){
            //throw new Common_Exception(Common_ExceptionCodes::DESK_PARAM_ERROR, '参数异常');
            return $arrOutput;
        }

        //根据类型获取通知(新通知/历史通知)

        if($id > 0){
            $arrConds['id']      = [$id,self::$_type[$type]];
        }
        $arrConds['deleted'] = Service_Data_AssistantNotice::DELETED_NO;
        $arrConds['status']  = self::$_status[$type];
        $arrConds['type']    = $noticeType;
        //获取最近14天的未读消息数量
        $time = time() - 14 * 86400;
        $arrConds['createTime']   = array($time, '>' );

        //根据通知类型获取数据 没传类型默认是私人+业务uid全部的类型
        $personTypeNum = 0;
        $personTypeList = [];
        if(!$noticeType){
            unset($arrConds['type']);

            $arrConds[]            = 'type in (' . implode(',', [Service_Data_AssistantNotice::TYPE_NOTICE, Service_Data_AssistantNotice::TYPE_17, Service_Data_AssistantNotice::TYPE_CHECK, Service_Data_AssistantNotice::TYPE_20, Service_Data_AssistantNotice::TYPE_30]) . ')';
            $arrConds['personUid'] = $personUid;
            //私人维度取30条的原因：不用折起来
            $personTypeList = $this->_objDsAssistantNotice->getListByConds($arrConds,array(),'create_time','desc',0,self::NOTICE_PAGE_SIZE);
            if(is_array($personTypeList) && $personTypeList){
                $personTypeNum = count($personTypeList);
            }

            unset($arrConds['personUid']);
            unset($arrConds[0]);

            $arrConds[] = 'type in ('.implode(',',[
                    Service_Data_AssistantNotice::TYPE_CHECK,
                    Service_Data_AssistantNotice::TYPE_11,
                    Service_Data_AssistantNotice::TYPE_12 ,
                    Service_Data_AssistantNotice::TYPE_13 ,
                    Service_Data_AssistantNotice::TYPE_14 ,
                    Service_Data_AssistantNotice::TYPE_15 ,
                    Service_Data_AssistantNotice::TYPE_16 ,
                    Service_Data_AssistantNotice::TYPE_18 ,
                    Service_Data_AssistantNotice::TYPE_19 ,
                    Service_Data_AssistantNotice::TYPE_21 ,
                    Service_Data_AssistantNotice::TYPE_22 ,
                    Service_Data_AssistantNotice::TYPE_23 ,
                    Service_Data_AssistantNotice::TYPE_Device_Notify ,
                ]).')';
            $arrConds['assistantUid'] = $assistantUid;

            $limit = 1000;

        }elseif (in_array($noticeType,[Service_Data_AssistantNotice::TYPE_NOTICE,Service_Data_AssistantNotice::TYPE_17])){
            //通知、云学堂 是私人维度
            $arrConds['personUid'] = $personUid;
            $limit = 100;
        }elseif ($noticeType == Service_Data_AssistantNotice::TYPE_CHECK && $assistantUid){
            //质检的场景比较特殊,存在真人和业务共存的场景 ，优先级 业务uid > 私人uid
            $arrConds['assistantUid'] = $assistantUid;
            $limit = 100;
        }elseif ($noticeType == Service_Data_AssistantNotice::TYPE_CHECK && $personUid){

            $arrConds['personUid'] = $personUid;
            $limit = 100;
        }elseif ($noticeType == Service_Data_AssistantNotice::TYPE_20){
            // 挽单；可能是辅导、可能是督学；可能有资产、可能无资产
            $arrConds[] = ' (assistant_uid = ' . $assistantUid . ' or person_uid = ' . $personUid . ') ';
            $limit = 100;
        }else{
            //其他通知类型都是业务uid维度
             if(!$assistantUid){
                 return $arrOutput;
             }

            $arrConds['assistantUid'] = $assistantUid;
            $limit = 100;
        }

        if(!$noticeType && !$assistantUid){
            $noticeList = [];
        }else{
            $noticeList = $this->_objDsAssistantNotice->getListByConds($arrConds, array(), 'create_time', 'desc', 0, $limit);
        }

        if(false === $noticeList){
            //throw new Common_Exception(Common_ExceptionCodes::DESK_DB_SELECT_FAILED, '获取辅导老师通知列表异常');
            return $arrOutput;
        }

        //对未读通知信息做聚合
        if($type  == self::TYPE_GET_NEW_NOTICE){
            $noticeList = $this->_objDsAssistantNotice->unReadNoticeAggregate($noticeList);
        }

        //第一次获取未读数据不足一页条数，需补充已读数据,全部类型时补充的已读数据是业务维度的，必须有assistantUid
        $unReadLen = count($noticeList) + $personTypeNum;
        $readNoticeList = [];
        if (($unReadLen < self::NOTICE_PAGE_SIZE) && $id == 0 ) {

            if(($assistantUid && !$noticeType) || $noticeType){
                $arrConds['status'] = Service_Data_AssistantNotice::NOTICE_READ_OK;

                //为避免limit 0,1 全量扫描 id 主键索引，每次固定取self::NOTICE_PAGE_SIZE条数据，再从结果集中取出addCount条数据作为补充数据
                $addCount = self::NOTICE_PAGE_SIZE - $unReadLen;
                $readNoticeList = $this->_objDsAssistantNotice->getListByConds($arrConds, array(), 'create_time', 'desc', 0, self::NOTICE_PAGE_SIZE);
                if(false === $readNoticeList){
                    //throw new Common_Exception(Common_ExceptionCodes::DESK_DB_SELECT_FAILED, '获取补充通知异常');
                    return $arrOutput;
                }
                $chunkNoticeList = array_chunk($readNoticeList, $addCount);
                $readNoticeList = empty($chunkNoticeList) ? [] : $chunkNoticeList[0];
            }

        }

        $noticeList = array_merge($noticeList, $personTypeList,$readNoticeList);

        if(empty($noticeList) || !is_array($noticeList)){
            return $arrOutput;
        }
        $noticeList = Tools_Array::sortByMultiCols($noticeList, ['id' => SORT_DESC]);
        //$id=0 表示第一页,则返回全部消息
        if($id > 0 ){
            $noticeList = array_slice($noticeList,0,self::NOTICE_PAGE_SIZE);
        }

        $firstNoticeId = $noticeList[0]['id'];
        $lastNoticeInfo = end($noticeList);
        $lastNoticeId = $lastNoticeInfo['id'];

        $courseIdKeyArr = [];
        $studentUidKeyArr = [];
        $studentCourseTypeArr = [
            Service_Data_AssistantNotice::TYPE_11 => 1,
            Service_Data_AssistantNotice::TYPE_12 => 1,
            Service_Data_AssistantNotice::TYPE_13 => 1,
            Service_Data_AssistantNotice::TYPE_14 => 1,
            Service_Data_AssistantNotice::TYPE_15 => 1,
            Service_Data_AssistantNotice::TYPE_16 => 1,
            Service_Data_AssistantNotice::TYPE_18 => 1,
            Service_Data_AssistantNotice::TYPE_19 => 1,
            Service_Data_AssistantNotice::TYPE_21 => 1,
            Service_Data_AssistantNotice::TYPE_22 => 1,
        ];
        //按照类型 进去区分
        $list = array();
        foreach ($noticeList as $notice){
            $row            = array();
            $row['id']      = $notice['id'];
            $row['type']    = $notice['type'];
            //$row['typeMap'] = Service_Data_AssistantNotice::$typeMap;
            $row['content'] = $notice['content'];
            $row['sourceId']    = $notice['sourceId'];
            $row['read']        = $notice['status'];
            $row['noticeTime']  = $notice['extData']['sourceTime'] ? date('Y-m-d H:i', $notice['extData']['sourceTime']) : date('Y-m-d H:i', $notice['createTime']);
            $row['noticeTimeInt']  = $notice['extData']['sourceTime'] ? $notice['extData']['sourceTime'] : $notice['createTime'];
            if(in_array($notice['type'], array(Service_Data_AssistantNotice::TYPE_11, Service_Data_AssistantNotice::TYPE_14, Service_Data_AssistantNotice::TYPE_20))){
                $row['studentUid']  = $notice['extData']['studentUid'];
                $row['courseId']    = $notice['extData']['courseId'];
            }
            $row['sourceUrl'] = '';
            $row['isAlert'] = 0;

            //如果是质检通知，多加一个跳转链接字段
            if($notice['type'] == Service_Data_AssistantNotice::TYPE_CHECK){
                $row['sourceUrl'] = $notice['extData']['sourceUrl'];
            }

            if($notice['type'] == Service_Data_AssistantNotice::TYPE_NOTICE){
                $row['isAlert'] = $notice['extData']['isAlert'];
            }
            //挽单通知根据taskType跳转不同的URL
            if($notice['type'] == Service_Data_AssistantNotice::TYPE_20){
                // 加一个新的跳转指引
                $row['directPage'] = $notice['extData']['directPage'];
                $row['sourceUrl'] = "/assistantdesk/view/lpctoolkit/refund";
            }

            // 设备通知
            if($notice['type'] == Service_Data_AssistantNotice::TYPE_Device_Notify) {
                $row['title']      = $notice['extData']['title'];
                $row['notice']     = $notice['extData']['notice'];
                $row['noticeType'] = $notice['extData']['noticeType'];
                $row['detailLink'] = $notice['extData']['detailLink'];
            }

            $row['isAggregate'] = 0;
            $row['aggregateNum'] = 0;
            if (isset($studentCourseTypeArr[$notice['type']])) {
                $courseIdKeyArr[$notice['extData']['params']['courseId']] = 1;
                $studentUidKeyArr[$notice['extData']['params']['studentUid']] = 1;
                $row['studentUid']  = $notice['extData']['params']['studentUid'];
                $row['courseId']    = $notice['extData']['params']['courseId'];
                $row['isAggregate'] = isset($notice['isAggregate']) ? $notice['isAggregate'] : 0;
                $row['aggregateNum'] = isset($notice['aggregateNum']) ? $notice['aggregateNum'] : 0;
            }

            //添加type hover显示文案
            $row['typeHover'] = isset(Service_Data_AssistantNotice::$typeHoverText[$notice['type']]) ? Service_Data_AssistantNotice::$typeHoverText[$notice['type']] : '';

            $list[] = $row;
        }

        //获取学生和课程信息，对通知信息进行格式化
        if (!empty($courseIdKeyArr)) {
            $nameMap = $this->nameMap(array_keys($courseIdKeyArr), array_keys($studentUidKeyArr));
            foreach ($list as $i => $n) {
                //格式化展示内容
                if (isset($studentCourseTypeArr[$n['type']])) {
                    $list[$i]['content'] = $this->stuCourseTypeContent($n, $nameMap['courseNameMap'], $nameMap['studentNameMap']);
                }
            }
        }

        //按照公告创建时间倒叙进行排序
        $list = Tools_Array::sortByMultiCols($list, array('noticeTimeInt' =>  SORT_DESC));
        $list = $this->listReOrder($list);

        $arrOutput['list'] = $list;
        $arrOutput['firstNoticeId'] = $firstNoticeId;
        $arrOutput['lastNoticeId'] = $lastNoticeId;

        return $arrOutput;
    }

    /**
     * 根据通知type生成对应content
     * @param $notice array
     * @param $courseNameMap array
     * @param $studentNameMap array
     * @return string
     */
    private function stuCourseTypeContent($notice, $courseNameMap, $studentNameMap){
        $courseName = isset($courseNameMap[$notice['courseId']]) ? $courseNameMap[$notice['courseId']] : '';
        $studentName = isset($studentNameMap[$notice['studentUid']]) ? $studentNameMap[$notice['studentUid']] : '';
        if ($notice['type'] === Service_Data_AssistantNotice::TYPE_11) {
            // 报名
            $stuStr = $notice['isAggregate'] ? $studentName . '等' . $notice['aggregateNum'] . '名学员' : $studentName . '学员';
            return $stuStr . '报名' . $courseName . "，请及时给小同学分班";
        } elseif ($notice['type'] === Service_Data_AssistantNotice::TYPE_12) {
            // 转入
            $stuStr = $notice['isAggregate'] ? $studentName . '等' . $notice['aggregateNum'] . '名学员' : $studentName . '学员';
            return $stuStr . '转入' . $courseName . "，请及时给小同学分班";
        } elseif ($notice['type'] === Service_Data_AssistantNotice::TYPE_18) {
            // 调小班
            $stuStr = $notice['isAggregate'] ? $studentName . '等' . $notice['aggregateNum'] . '名学员' : $studentName . '学员';
            return $courseName . $stuStr . '小班发生变动';
        } elseif ($notice['type'] === Service_Data_AssistantNotice::TYPE_15 || $notice['type'] === Service_Data_AssistantNotice::TYPE_16 || $notice['type'] === Service_Data_AssistantNotice::TYPE_21 || $notice['type'] === Service_Data_AssistantNotice::TYPE_22) {
            return $studentName . '在' . $courseName . '中' . Service_Data_AssistantNotice::$typeMap[$notice['type']];
        } else {
            $stuStr = $notice['isAggregate'] ? $studentName . '等' . $notice['aggregateNum'] . '名学员' : $studentName;
            $courseStr = $notice['isAggregate'] ? $courseName . '，点击查看' : $courseName;
            return $stuStr . Service_Data_AssistantNotice::$typeMap[$notice['type']] . $courseStr;
        }
    }

    /**
     * 调整未读通知与已读通知位置，将全部未读通知信息放到全部已读通知信息前面
     * @param $noticeList
     * @return array
     */
    private function listReOrder($noticeList){
        if (empty($noticeList) || !is_array($noticeList)) {
            return [];
        }
        $readNotices = [];
        $unReadNotices = [];
        foreach ($noticeList as $i => $notice) {
            if ($notice['read'] === Service_Data_AssistantNotice::NOTICE_READ_OK) {
                $readNotices[] = $notice;
            }
            if ($notice['read'] === Service_Data_AssistantNotice::NOTICE_READ_NO) {
                $unReadNotices[] = $notice;
            }
        }
        if (empty($unReadNotices)) {
            return $readNotices;
        } else {
            return array_merge($unReadNotices, $readNotices);
        }
    }

    /**
     * 获取名称映射
     * @param array $uniqueCourseIds
     * @param array $uniqueStudentUids
     * @throws Common_Exception
     * @return array
     */
    private function nameMap($uniqueCourseIds, $uniqueStudentUids){
        //获取课程信息
        $arrConds = [
            'course_id in (' . implode(',', $uniqueCourseIds) . ')',
        ];
        $courseList = $this->_objDsAssistantNewCourse->getListByConds($arrConds, ['courseId', 'courseName']);
        if ($courseList === false) {
            //throw new Common_Exception(Common_ExceptionCodes::DESK_DB_SELECT_FAILED, '获取课程信息异常');
            return ['courseNameMap' => [], 'studentNameMap' => []];
        }
        $courseNameMap = Tools_Array::getArrayColumn($courseList, 'courseName', 'courseId');
        //获取学生信息
        $studentList = Api_Dau::getStudents($uniqueStudentUids, ['studentUid', 'studentName']);
        if ($studentList === false) {
            //throw new Common_Exception(Common_ExceptionCodes::DESK_DB_SELECT_FAILED, '获取学生信息异常');
            return ['courseNameMap' => [], 'studentNameMap' => []];
        }
        $studentNameMap = Tools_Array::getArrayColumn($studentList, 'studentName', 'studentUid');
        return ['courseNameMap' => $courseNameMap, 'studentNameMap' => $studentNameMap];
    }

}
