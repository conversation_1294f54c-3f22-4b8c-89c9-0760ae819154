<?php
/**
 * Created by PhpStorm.
 * User: 庞俊超<<EMAIL>>
 * Date: 2018/5/25
 * Time: 16:59
 * 获取未读消息数量  全部通知类型  业务维度+真人维度
 */
class Service_Page_Desk_Task_NoticeUnreadNum{

    private $_objDsAssistantNotice;

    public function __construct(){
        $this->_objDsAssistantNotice     = new Service_Data_AssistantNotice();
    }

    public function execute($arrInput){
        $personUid       = $arrInput['personUid'];
        $assistantUid    = $arrInput['assistantUid'];

        $arrOutput = array('num' => 0);

        //非辅导老师系统用户，$personUid小于等于0，直接返回未读数为0，不抛异常 2019-12-11 单洪峰
        if( 0 >= $personUid){
            return $arrOutput;
        }

        //获取最近14天的未读消息数量
        $time = time() - 14 * 86400;
        //非聚合类型   获取业务账号的未读条数
        $unAggregationType = [
        Service_Data_AssistantNotice::TYPE_CHECK,
            Service_Data_AssistantNotice::TYPE_12,
            Service_Data_AssistantNotice::TYPE_13,
            Service_Data_AssistantNotice::TYPE_14,
            Service_Data_AssistantNotice::TYPE_15,
            Service_Data_AssistantNotice::TYPE_16,
            Service_Data_AssistantNotice::TYPE_19,
            Service_Data_AssistantNotice::TYPE_20,
            Service_Data_AssistantNotice::TYPE_21,
            Service_Data_AssistantNotice::TYPE_22,
            Service_Data_AssistantNotice::TYPE_23,
            Service_Data_AssistantNotice::TYPE_Device_Notify,
        ];

        //先查私人维度上的条数
        $arrConds = array(
            'createTime'   => array($time, '>' ),
            'status'       => Service_Data_AssistantNotice::NOTICE_READ_NO,
            'deleted'      => Service_Data_AssistantNotice::DELETED_NO,
        );

        $personNoticeType = [
            Service_Data_AssistantNotice::TYPE_CHECK,
            Service_Data_AssistantNotice::TYPE_NOTICE,
            Service_Data_AssistantNotice::TYPE_17,
            Service_Data_AssistantNotice::TYPE_30,

        ];

        $arrConds['personUid'] = $personUid;
        $arrConds[]          = 'type in ('. implode(',', $personNoticeType) .')';

        $personNoticeTypeNum = $this->_objDsAssistantNotice->getCount($arrConds);
        if ($personNoticeTypeNum === false) {
            return $arrOutput;
        }

        $num =  $aggregateNotices = 0;
        if($assistantUid){
            unset($arrConds['personUid']);
            unset($arrConds[0]);

            $arrConds[]          = 'type in ('. implode(',', $unAggregationType) .')';
            $arrConds['assistantUid']    = $assistantUid;

            $num = $this->_objDsAssistantNotice->getCount($arrConds);
            if(false === $num){
                //throw new Common_Exception(Common_ExceptionCodes::DESK_DB_SELECT_FAILED, '获取未读数量失败');
                return $arrOutput;
            }

            //聚合类型未读数计算
            $arrConds = [
                'assistantUid' => $assistantUid,
                'createTime'   => array($time, '>' ),
                'status'       => Service_Data_AssistantNotice::NOTICE_READ_NO,
                'deleted'      => Service_Data_AssistantNotice::DELETED_NO,
                'type in ('.Service_Data_AssistantNotice::TYPE_11.','.Service_Data_AssistantNotice::TYPE_18.')',
            ];
            $applyNotices = $this->_objDsAssistantNotice->getListByConds($arrConds);
            if ($applyNotices === false) {
                //throw new Common_Exception(Common_ExceptionCodes::DESK_DB_SELECT_FAILED, '获取报名未读数据失败');
                return $arrOutput;
            }
            $aggregateNotices = $this->_objDsAssistantNotice->unReadNoticeAggregate($applyNotices);
            $aggregateNotices = count($aggregateNotices);
        }

        $arrOutput['num'] = $num +  $personNoticeTypeNum + $aggregateNotices;
        return $arrOutput;
    }
}