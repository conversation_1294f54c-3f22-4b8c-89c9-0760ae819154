<?php

/**
 * @file DataCenter.php
 * <AUTHOR>
 * @date 2021/7/13 14:02:30
 * @brief 数据中心，筛选功能与外部系统的交互，获取数据，内部实现内存缓存，保证一次请求，外部接口只调用一次
 **/
class AssistantDesk_Data_Duxuesc_DataCenter
{


    //巩固练习状态
    const PRACTICE_STATUS_NOHAVA        = 0;//未布置
    const PRACTICE_STATUS_NOCOMMIT      = 1;//未提交
    const PRACTICE_STATUS_COMMIT        = 2;//已提交
    const PRACTICE_STATUS_WAITCORRECT   = 3;//待批改
    const PRACTICE_STATUS_WAITRECORRECT = 4;//待重批
    const PRACTICE_STATUS_WAITRECOMMIT  = 5;//待重提
    const PRACTICE_STATUS_RECORRECTED   = 6;//已订正


    /** datacenter需要的参数 start */
    private static $lessonIds = null; //章节id数组
    /** datacenter需要的参数 end */

    /** datacenter需要缓存的数据 start */
    private static $allLeadsInfos           = null;//数据库获取的所有例子信息
    private static $lastFromListMap         = null;//lastfrom列表
    private static $systemIntentionScoreMap = null;//系统意向值
    private static $coursePurchaseMap       = null;//课程购买
    private static $leadsInfos              = null;//lpcleads服务获取的例子基础信息
    private static $expiredLeadsMap         = null;//失效例子信息
    private static $studentIdentityInfo     = null;//学生的城市等级信息
    private static $bottomTestSubjectList   = null;//摸底测科目列表
    private static $surveyMap               = null;//课程绑定的问卷
    private static $formatFnDemandSurveyLinkShortUrl = null; // 课程维度蜂鸟挖需问卷绑定链接
    private static $surveyList              = null;//问卷详情
    private static $surveyUrl               = null;//问卷url
    private static $surveyInfoMap           = null;//survey信息
    private static $fnActivityStudentUids   = null;//参加蜂鸟活动的学员id列表
    private static $fnDemandStudentUids     = null;//参加挖需问卷学员id列表
    private static $fnActivityRecords       = null;//参加蜂鸟活动的学员列表
    private static $fnActivityStudentData   = null;//参加蜂鸟活动的学员预约信息
    private static $leadsSliceScoreList     = null;//用户分层数据
    private static $leadsSliceScoreListAll  = null;//用户分层数据
    private static $studentCourseData       = null;//学生购物车中的课程数据
    private static $addFriendSmsInfo        = null;//发送加好友短信信息
    private static $leadsShareUrl           = null;//高光时刻url
    private static $vipRightsUserList       = null;//用户会员信息
    private static $studentAnswerMap        = null;//学生问卷作答信息
    private static $intellectCallOutList    = null;//智能外呼
    private static $dbLeadsLessonsInfos     = null;//章节信息
    private static $courseExpiredTime       = null;//课程的到期时间
    private static $xlCourseTradeList       = null;//小鹿订单列表
    private static $homeworkData            = null;//口算数据
    private static $preOrderInfo            = null;//预付款信息,定金膨胀功能
    private static $transList               = null;//转化数据
    private static $studentCoupons          = null;//学生专题课优惠券状态
    private static $getLeadsExtInfo         = null;//例子额外信息
    /** datacenter需要缓存的数据 end */

    private static $studentAnswerMapShowInfo = null;//学生问卷作答信息,无网络请求

    public static function setLessonIds($lessonIds) {
        self::$lessonIds = $lessonIds;
    }

    public static function getLessonIds() {
        if (self::$lessonIds) {
            return self::$lessonIds;
        }
        $courseInfo      = self::getCourseInfo();
        $lessonInfos     = $courseInfo['lessonList'];
        self::$lessonIds = array_keys($lessonInfos);
        return self::$lessonIds;
    }

    public static function getLeadsStudentMap() {
        return AssistantDesk_Data_CommonParams::$leadsIdMapStudentUid;
    }

    public static function getLeadsIds() {
        return array_keys(AssistantDesk_Data_CommonParams::$leadsIdMapStudentUid);
    }

    public static function getStudentUids() {
        return AssistantDesk_Data_CommonParams::$studentUids;
    }

    /**
     * 获取课程信息
     * @return array|null
     */
    public static function getCourseInfo() {
        return AssistantDesk_Course::getCourseInfo(AssistantDesk_Data_CommonParams::$courseId);

    }

    /**
     * 获取章节playType
     *
     * @return int|mixed
     */
    public static function getLessonPlayType() {
        $courseInfo = self::getCourseInfo();
        $lessonId   = AssistantDesk_Data_CommonParams::$lessonId;
        return $courseInfo['lessonList'][$lessonId]['playType'] ?? Api_Dal::PLAY_TYPE_LIVE;
    }

    /**
     * 获取lpcUid+courseId+wxId下所有的例子信息
     */
    public static function getAllLeadsInfos() {
        if (self::$allLeadsInfos !== null) {
            return self::$allLeadsInfos;
        }
        $courseId   = AssistantDesk_Data_CommonParams::$courseId;
        $lpcUid     = AssistantDesk_Data_CommonParams::$personUid;
        $leadsIds   = self::getLeadsIds();
        $leadsInfos = self::getLeadsInfosByUidCidleadsIds($lpcUid, $courseId, $leadsIds);
        if (empty($leadsInfos)) {
            $leadsInfos = [];
        }

        self::$allLeadsInfos = array_column($leadsInfos, null, 'leadsId');
        return self::$allLeadsInfos;
    }

    /**
     * 根据lpcUid、courseId和leadsIds获取所有的例子信息,最多取2000个
     * @param $lpcUid
     * @param $courseId
     * @param $leadsIds
     * @return array|false
     */
    private static function getLeadsInfosByUidCidleadsIds($lpcUid, $courseId, $leadsIds) {
        if (empty($leadsIds) || !is_array($leadsIds)) {
            return [];
        }
        if (empty($lpcUid) || empty($courseId)) {
            return [];
        }
        $cond = [
            'scUid'    => $lpcUid,
            'courseId' => $courseId,
            self::getStatusCond()
        ];

        $cond[] = 'leads_id in (' . implode(',', $leadsIds) . ')';

        $dao   = new Dao_ScLeads();
        $field = $dao->getAllFiled();

        $arrAppends = [
            "order by id desc",
            "limit 0, 2000",
        ];
        $ret        = $dao->getListByConds($lpcUid, $cond, $field, NULL, $arrAppends);

        if (empty($ret)) {
            return [];
        }
        $dauStudentInfoMap = Api_Dau::getStudents(array_column($ret, 'customUid'));
        foreach ($ret as $k => &$v) {
            $v['studentName'] = isset($dauStudentInfoMap[$v['customUid']]) ? $dauStudentInfoMap[$v['customUid']]['studentName'] : $v['studentName'];
        }
        return $ret;
    }

    private static function getStatusCond() {
        $status = [
            Dao_ScLeads::STATUS_UNTRANS,
            Dao_ScLeads::STATUS_TRANSED,
            Dao_ScLeads::STATUS_INVALID
        ];

        return 'status in (' . implode(',', $status) . ')';
    }

    public static function getLeadsData() {
        return self::getAllLeadsInfos();
    }

    public static function getAddFriendSmsInfo() {
        if (self::$addFriendSmsInfo !== null) {
            return self::$addFriendSmsInfo;
        }
        $leadsData = self::getLeadsData();
        $wxIds     = array_unique(array_filter(array_column($leadsData, 'wxId')));
        if (empty($wxIds) || !is_array($wxIds)) {
            self::$addFriendSmsInfo = [];
            return self::$addFriendSmsInfo;
        }
        $qwIds = [];
        foreach ($wxIds as $_wxId) {
            $wxInfo = Api_Laxinmis_Lpc::getScWxInfoById($_wxId);
            if (isset($wxInfo['wxType']) && $wxInfo['wxType'] == 2) {
                $qwIds[] = $_wxId;
            }
        }
        if (empty($qwIds)) {
            self::$addFriendSmsInfo = [];
            return self::$addFriendSmsInfo;
        }
        $ret = [];
        foreach ($qwIds as $_qwId) {
            $ret[$_qwId] = [];
        }
        $objDsBatchSms = new Service_Data_BatchSms();
        $lpcUid        = AssistantDesk_Data_CommonParams::$personUid;
        $courseId      = AssistantDesk_Data_CommonParams::$courseId;
        $taskList      = $objDsBatchSms->getAddFriendSmsTask($lpcUid, $courseId, $wxIds);
        if (empty($taskList)) {
            self::$addFriendSmsInfo = $ret;
            return self::$addFriendSmsInfo;
        }
        $taskIdWxIdMap = array_column($taskList, 'wxId', 'id');
        $taskIds       = array_column($taskList, 'id');
        $detailList    = $objDsBatchSms->getDetailListByTaskIds($taskIds);
        if (empty($detailList)) {
            self::$addFriendSmsInfo = $ret;
            return self::$addFriendSmsInfo;
        }
        foreach ($detailList as $_detail) {
            $_qwId                          = $taskIdWxIdMap[$_detail['taskId']];
            $ret[$_qwId][$_detail['uid']][] = $_detail['createTime'];
        }
        self::$addFriendSmsInfo = $ret;
        return self::$addFriendSmsInfo;
    }

    /**
     * 获取系统意向值
     * @return array|null
     */
    public static function getSystemIntentionScoreMap() {
        if (self::$systemIntentionScoreMap !== null) {
            return self::$systemIntentionScoreMap;
        }
        $courseId = AssistantDesk_Data_CommonParams::$courseId;
        $lpcUid   = AssistantDesk_Data_CommonParams::$personUid;
        $key      = sprintf(Duxuesc_Const::DUXUESC_LEADS_SYSTEM_INTENTION_CACHE_KEY, date('Ymd'), $courseId);
        $ret      = AssistantDesk_Codis::getInstance()->zRange($key, 0, -1, 'WITHSCORES');
        if (empty($ret)) {
            self::$systemIntentionScoreMap = [];
            return self::$systemIntentionScoreMap;
        }
        $map = [];
        foreach ($ret as $key => $value) {
            list($uid, $leadsId) = explode('_', $key);

            if ($uid
                && $uid == $lpcUid
                && $leadsId
            ) {
                $map[$leadsId] = $value;
            }
        }
        self::$systemIntentionScoreMap = $map;
        return self::$systemIntentionScoreMap;
    }

    public static function getLeadsShareUrlByCourseAndLpc() {
        if (self::$leadsShareUrl !== null) {
            return self::$leadsShareUrl;
        }
        $courseId  = AssistantDesk_Data_CommonParams::$courseId;
        $lpcUid    = AssistantDesk_Data_CommonParams::$personUid;
        $shareUrls = (new Service_Data_LeadsCourseReview())->getLeadsShareUrlByCourseAndLpc($courseId, $lpcUid);
        if (empty($shareUrls)) {
            $shareUrls = [];
        }
        self::$leadsShareUrl = $shareUrls;
        return self::$leadsShareUrl;
    }

    /**
     * 调用需要before时期预加字段      如 AssistantDesk_Data_DataSource::addLeadsFields(["leads_id",'lpc_uid','course_id',]);
     * 获取课程例子维度的es数据
     * @return array|null
     */
    public static function getLpcLeadsCourseEsData() {
        $leadsIds     = self::getLeadsIds();
        $courseId     = AssistantDesk_Data_CommonParams::$courseId;
        $lpcUid       = AssistantDesk_Data_CommonParams::$personUid;
        $memFilter    = [
            'lpc_uid'   => $lpcUid,
            'course_id' => $courseId,
        ];
        $lpcLeadsData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsLeadsData", [
            $leadsIds,
        ]);
        $lpcLeadsData = self::filterByMemory($lpcLeadsData, $memFilter);
        if (empty($lpcLeadsData)) {
            $lpcLeadsData = [];
        }

        return $lpcLeadsData;
    }

    /**
     * 内存过滤结果 保留list key 值
     * @param $list array|mixed 数据结果list
     * @param $filterArr array 过滤条件 如['status'=>1]
     * @return array|mixed
     */
    private static function filterByMemory($list, $filterArr) {
        if (!is_array($list)) {
            return $list;
        }
        if (!$filterArr || !is_array($filterArr)) {
            return $list;
        }
        foreach ($list as $k => $v) {
            foreach ($filterArr as $filterKey => $filterValue) {
                if (($v[$filterKey] ?? null) != $filterValue) {
                    unset($list[$k]);
                    break;
                }
            }
        }
        return $list;
    }

    /**
     * 相同参数调用只调用一次
     * @param string $key
     * @param callable $func
     * @return mixed
     */
    public static function callOnce(string $key, callable $func) {
        static $res = [];
        if (!array_key_exists($key, $res)) {
            $res[$key] = $func();
        }
        return $res[$key];
    }


    /**
     * 调用时需要预加字段 AssistantDesk_Data_DataSource::addUFields(["student_uid"]);
     * @return mixed
     * @throws ReflectionException
     */
    public static function getValidStudentUids() {
        $studentUids = self::getStudentUids();
        $studentList = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsUData", [
            $studentUids,
        ]);
        return $studentList;
    }

    public static function getBcStallmanList() {
        return self::callOnce(__METHOD__ . AssistantDesk_Data_CommonParams::$lessonId, function () {
            $studentUids = self::getStudentUids();
            return Api_Stallman_Api::listTask($studentUids, AssistantDesk_Data_CommonParams::$lessonId);
        });
    }

    /**
     * 获取课程购买记录
     * @return array|null
     */
    public static function getCoursePurchaseMap() {
        if (self::$coursePurchaseMap !== null) {
            return self::$coursePurchaseMap;
        }
        $courseId      = AssistantDesk_Data_CommonParams::$courseId;
        $studentUids   = self::getStudentUids();
        $courseInfo    = self::getCourseInfo();
        $newCourseType = $courseInfo['newCourseType'];

        $coursePurchaseMap = Api_Dar::getCoursePurchaseMapByCidUidsNewUserType($courseId, $studentUids, $newCourseType);
        if (empty($coursePurchaseMap)) {
            $coursePurchaseMap = [];
        }
        self::$coursePurchaseMap = $coursePurchaseMap;
        return self::$coursePurchaseMap;
    }

    /**
     * 从lpcleads服务获取例子信息
     * @return array|mixed|null
     */
    public static function getLeadsInfos() {
        if (self::$leadsInfos !== null) {
            return self::$leadsInfos;
        }
        $leadsIds   = self::getLeadsIds();
        $leadsInfos = Api_Lpcleads_Lpcleads::batchGetLeadsByIds($leadsIds);
        if (empty($leadsInfos)) {
            $leadsInfos = [];
        }
        self::$leadsInfos = $leadsInfos;
        return self::$leadsInfos;
    }

    /**
     * 获取失效例子列表
     * @return array
     */
    public static function getExpiredLeadsMap() {
        if (self::$expiredLeadsMap !== null) {
            return self::$expiredLeadsMap;
        }
        $nowTime           = time();
        $courseExpiredTime = self::getCourseExpiredTime();
        if ($courseExpiredTime < $nowTime) {
            self::$expiredLeadsMap = [];
            return self::$expiredLeadsMap;
        }
        $lpcLeadsInfo = self::getLeadsInfos();
        $resList      = [];
        foreach ($lpcLeadsInfo as $leadsInfo) {
            if ($leadsInfo['status'] == 3) {
                $resList[$leadsInfo['leadsId']] = $leadsInfo;
            }
        }
        self::$expiredLeadsMap = $resList;
        return self::$expiredLeadsMap;
    }

    /**
     * 获取摸底测的科目列表
     * @return array|null
     */
    public static function getBottomTestSubjectList() {
        if (self::$bottomTestSubjectList !== null) {
            return self::$bottomTestSubjectList;
        }
        $courseInfo = self::getCourseInfo();
        $cpuId      = $courseInfo['cpuId'] ?? 0;
        if (empty($cpuId)) {
            self::$bottomTestSubjectList = [];
            return self::$bottomTestSubjectList;
        }
        $subjectList = Service_Data_Exam_ExamCore::getUrgeBottomTestSubjectList($cpuId);
        if (empty($subjectList)) {
            $subjectList = [];
        }
        self::$bottomTestSubjectList = $subjectList;
        return self::$bottomTestSubjectList;
    }

    /**
     * 获取学生的城市信息
     * @return array|null
     */
    public static function getStudentIdentityInfo() {
        if (self::$studentIdentityInfo !== null) {
            return self::$studentIdentityInfo;
        }
        $leadIdMapStudentUids = self::getLeadsStudentMap();
        $studentIdentityInfo  = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsLeadsDataCityInfoByStudentUids", [
            $leadIdMapStudentUids,
        ]);
        if (empty($studentIdentityInfo)) {
            $studentIdentityInfo = [];
        }
        self::$studentIdentityInfo = $studentIdentityInfo;
        return self::$studentIdentityInfo;
    }

    /**
     * $surveyMap
     * @return array|null
     */
    public static function getSurveyMap($cash = true) {
        if (self::$surveyMap !== null && $cash) {
            return self::$surveyMap;
        }
        $courseId  = AssistantDesk_Data_CommonParams::$courseId;
        $surveyMap = (new Service_Data_BindInfo())->getBindInfoByCourseId($courseId, 1, ['surveyId', 'answerId','surveyType', 'courseId', 'id']);
        if (empty($surveyMap)) {
            $surveyMap = [];
        }
        self::$surveyMap = $surveyMap;
        return self::$surveyMap;
    }

    public static function getShortUrl($actId) {
        if (self::$formatFnDemandSurveyLinkShortUrl) {
            return self::$formatFnDemandSurveyLinkShortUrl;
        }

        //return sprintf("https://sell-ass1021-e.suanshubang.cc/fengniao/p/%s?actId=%s&groupId=0&enforceWK=1",$actId,$actId);
        self::$formatFnDemandSurveyLinkShortUrl  = Api_Su::getShortUrl(sprintf("https://www.fengniaojianzhan.com/fengniao/p/%s?actId=%s&groupId=0&enforceWK=1",$actId,$actId));

        return self::$formatFnDemandSurveyLinkShortUrl;
    }



    /**
     * $surveyList
     * @return array|null
     */
    public static function getSurveyList() {
        if (self::$surveyList !== null) {
            return self::$surveyList;
        }
        $surveyMap = self::getSurveyMap();
        if (empty($surveyMap)) {
            self::$surveyList = [];
            return self::$surveyList;
        }
        $surveyId   = array_column($surveyMap, 'surveyId');
        $surveyList = (new Service_Data_Survey())->getSurveyList($surveyId, 1);
        if (empty($surveyList)) {
            $surveyList = [];
        }
        self::$surveyList = $surveyList;
        return self::$surveyList;
    }

    public static function getSurveyUrl() {
        if (self::$surveyUrl !== null) {
            return self::$surveyUrl;
        }
        $bindInfo = self::getSurveyMap();
        if (empty($bindInfo)) {
            self::$surveyUrl = [];
            return self::$surveyUrl;
        }
        $courseId = AssistantDesk_Data_CommonParams::$courseId;
        $data     = ['needSurveyUrl' => '', 'orderSurveyUrl' => ''];
        foreach ($bindInfo as $val) {
            $surveyId = $val['surveyId'];
            if ($val['surveyType'] == Service_Data_BindInfo::NEED_TYPE) {
                $data['needSurveyUrl']         = Api_Su::getShortUrl('https://jingpinke.zuoyebang.com/alita/paper?&courseId=' . Hk_Util_IdCrypt::encodeUid($courseId) . '&surveyId=' . $surveyId . '&surveyType=' . Service_Data_BindInfo::NEED_TYPE . '&lastfrom=lpcxueqing');
                $data['noCourseNeedSurveyUrl'] = Api_Su::getShortUrl('https://jingpinke.zuoyebang.com/alita/paper?&courseId=' . Hk_Util_IdCrypt::encodeUid($courseId) . '&surveyId=' . $surveyId . '&surveyType=' . Service_Data_BindInfo::NEED_TYPE . '&isNoCourse=1&lastfrom=lpcxueqing');
            }
            if ($val['surveyType'] == Service_Data_BindInfo::ORDER_TYPE) {
                $data['orderSurveyUrl']         = Api_Su::getShortUrl('https://jingpinke.zuoyebang.com/alita/paper?&courseId=' . Hk_Util_IdCrypt::encodeUid($courseId) . '&surveyId=' . $surveyId . '&surveyType=' . Service_Data_BindInfo::ORDER_TYPE . '&lastfrom=lpcxueqing');
                $data['noCourseOrderSurveyUrl'] = Api_Su::getShortUrl('https://jingpinke.zuoyebang.com/alita/paper?&courseId=' . Hk_Util_IdCrypt::encodeUid($courseId) . '&surveyId=' . $surveyId . '&surveyType=' . Service_Data_BindInfo::ORDER_TYPE . '&isNoCourse=1&lastfrom=lpcxueqing');
            }
        }
        self::$surveyUrl = $data;
        return self::$surveyUrl;
    }

    public static function getStudentIntellectCallOutList() {
        if (self::$intellectCallOutList !== null) {
            return self::$intellectCallOutList;
        }
        $studentUids = self::getStudentUids();
        $courseId    = AssistantDesk_Data_CommonParams::$courseId;

        $courseInfo = self::getCourseInfo();
        if (!in_array($courseInfo['mainGradeId'], Service_Data_Agg_Common::getIntellectCallOutGrade())) {
            self::$intellectCallOutList = [];
            return [];
        }

        $intellectCallOutList = Service_Data_Agg_Common::getStudentIntellectCallOutList($courseId, $studentUids);
        if (empty($intellectCallOutList)) {
            $intellectCallOutList = [];
        }
        self::$intellectCallOutList = $intellectCallOutList;
        return self::$intellectCallOutList;
    }

    public static function getAllStuPreOrderInfos() {
        if (self::$preOrderInfo !== null) {
            return self::$preOrderInfo;
        }
        $studentUids = self::getStudentUids();
        if (empty($studentUids)) {
            self::$preOrderInfo = [];
            return self::$preOrderInfo;
        }
        $list = (new Service_Data_PreOrderList())->getAllStuPreOrderInfos($studentUids);
        if (empty($list)) {
            self::$preOrderInfo = [];
            return self::$preOrderInfo;
        }
        return array_column($list, null, 'studentUid');
    }

    private static function _getHomeworkGrade($courseId, $kpUid) {
        $ralRet = Api_Parenthomework_Homework::getLpcActiveInfo(intval($courseId), intval($kpUid));
        if (empty($ralRet)) {
            return [];
        }
        return $ralRet['list'];
    }

    /**
     * 获取lastfrom列表
     * @return array|null
     */
    public static function getLastfromListMap() {
        if (self::$lastFromListMap !== null) {
            return self::$lastFromListMap;
        }
        $lastFromListMap = (new Service_Data_Lastfrom())->getLastfromLabelListMap();
        if (empty($lastFromListMap)) {
            $lastFromListMap = [];
        }
        self::$lastFromListMap = $lastFromListMap;
        return self::$lastFromListMap;
    }

    /**
     * 重点关注列表
     * @return array|null
     */
    public static function getFocusList() {
        static $focusList = [];
        $courseId = AssistantDesk_Data_CommonParams::$courseId;
        $lessonId = AssistantDesk_Data_CommonParams::$lessonId;
        if (isset($focusList[$courseId][$lessonId])) {
            return $focusList[$courseId][$lessonId];
        }
        $arrConds = array(
            'lessonId' => $lessonId,
            'focus'    => Service_Data_LessonStudent::FOCUS_OK,
            'status'   => Service_Data_LessonStudent::STATUS_OK,
        );

        $assistantLessonStudentList = (new Service_Data_LessonStudent())->getListByConds($courseId, $arrConds, array('studentUid', 'createTime'));
        !$assistantLessonStudentList && $assistantLessonStudentList = [];
        $focusList[$courseId][$lessonId] = array_column($assistantLessonStudentList, 'studentUid');

        return $focusList[$courseId][$lessonId];
    }

    public static function getInclassStudentList($lessonId, $studentUids) {
        return Common_Singleton::getInstanceData(Api_Inclass::class, "getInclassStudentList", [
            $lessonId,
            $studentUids,
        ]);
    }

    /**
     * 获取学生的作文报告
     * @return array|null
     */
    public static function getCompositionReport() {

        static $compositionReport = [];
        $courseId = AssistantDesk_Data_CommonParams::$courseId;
        $lessonId = AssistantDesk_Data_CommonParams::$lessonId;
        if (isset($compositionReport[$courseId][$lessonId])) {
            return $compositionReport[$courseId][$lessonId];
        }
        $compositionReport[$courseId][$lessonId] = [];
        $studentUids                             = self::getStudentUids();
        if (empty($studentUids)) {
            return $compositionReport[$courseId][$lessonId];
        }
        $courseInfo  = self::getCourseInfo();
        $courseGrade = $courseInfo['grades'][0];
        if (!in_array($courseGrade, Duxuesc_Const::PRE_PRIMARY_SCHOOL)) {
            return $compositionReport[$courseId][$lessonId];
        }
        $reportTid = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getLessonComposition");
        if (empty($reportTid) || empty($reportTid[$lessonId]['tidList'])) {
            return $compositionReport[$courseId][$lessonId];
        }
        $reportData  = [];
        $studentUids = array_chunk($studentUids, 200);
        foreach ($studentUids as $uids) {
            $data = Api_PcAssistant::lessonStudentCompositionReport($courseId, $lessonId, $uids);
            if (!empty($data['list'])) {
                $reportData = array_merge($reportData, $data['list']);
            }
        }

        if (empty($reportData)) {
            return $compositionReport[$courseId][$lessonId];
        }
        $compositionReport[$courseId][$lessonId] = array_column($reportData, null, 'studentUid');
        return $compositionReport[$courseId][$lessonId];
    }


    /**
     * 例子分层是否可用
     * @param $courseId
     * @param $lessonId
     * @param $serviceId
     * @param $serviceType
     * @param $lpcUid
     * @return false
     */
    public static function leadsSliceEnable($courseId, $lpcUid) {
        if (!$courseId) {
            return false;
        }
        if (!$lpcUid) {
            return false;
        }
        $courseInfo = self::getCourseInfo();
        return self::callOnce(__METHOD__ . md5(json_encode(func_get_args())), function () use ($courseInfo) {
            return in_array($courseInfo['mainGradeId'], Service_Data_Slice_Data::getLeadsSliceGrade());
        });
    }

    public static function getStudentLayerTag() {
        $data = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsExtendLeadsData", [
            AssistantDesk_Data_Duxuesc_DataCenter::getLeadsIds(),
        ]);
        return $data;
    }

    /**
     * 调用需要before状态时预加字段         AssistantDesk_Data_DataSource::addLpcLuFields(["leads_id",]);
     * @return array|mixed|null
     * @throws ReflectionException
     */
    public static function getLpcLeadsLessonEsData() {
        $leadsIds             = self::getLeadsIds();
        $courseId             = AssistantDesk_Data_CommonParams::$courseId;
        $lessonId             = AssistantDesk_Data_CommonParams::$lessonId;
        $lpcLeadsLessonEsData = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getEsLpcLuData", [
            $lessonId,
            $leadsIds,
            $courseId,
        ]);

        if (empty($lpcLeadsLessonEsData)) {
            $lpcLeadsLessonEsData = [];
        }
        return $lpcLeadsLessonEsData;
    }

    public static function getFnActivityStudentUids($cash = true) {
        if (self::$fnActivityStudentUids !== null && $cash) {
            return self::$fnActivityStudentUids;
        }

        $fnActiveRecodeMap = self::getFnActivityRecords($cash);
        if (empty($fnActiveRecodeMap)) {
            self::$fnActivityStudentUids = [];
            return self::$fnActivityStudentUids;
        }
        self::$fnActivityStudentUids = array_column($fnActiveRecodeMap, 'uid');
        return self::$fnActivityStudentUids;
    }

    //蜂鸟挖需问卷的学生id
    public static function getFnDemandStudentUids($cash = true) {
        if (self::$fnDemandStudentUids !== null && $cash) {
            return self::$fnDemandStudentUids;
        }

        $fnActiveRecodeMap = self::getFnActivityRecords($cash, 4);
        if (empty($fnActiveRecodeMap)) {
            self::$fnDemandStudentUids = [];
            return self::$fnDemandStudentUids;
        }
        self::$fnDemandStudentUids = array_column($fnActiveRecodeMap, 'uid');
        return self::$fnDemandStudentUids;
    }

    public static function getFnActivityStudentData($cash = true, $surveyType = 3) {
        if (isset(self::$fnActivityStudentData[$surveyType]) && $cash) {
            return self::$fnActivityStudentData[$surveyType];
        }

        $fnActiveRecodeMap = self::getFnActivityRecords($cash, $surveyType);
        if (empty($fnActiveRecodeMap)) {
            self::$fnActivityStudentData[$surveyType] = [];
            return self::$fnActivityStudentData[$surveyType];
        }
        self::$fnActivityStudentData[$surveyType] = array_column($fnActiveRecodeMap, null, 'uid');
        return self::$fnActivityStudentData[$surveyType];
    }

    public static function getFnActivityRecords($cache = true, $surveyType = 3) {
        if (isset(self::$fnActivityRecords[$surveyType]) && $cache) {
            return self::$fnActivityRecords[$surveyType];
        }
        $surveyMap = self::getSurveyMap($cache);
        if (empty($surveyMap)) {
            self::$fnActivityRecords[$surveyType] = [];
            return self::$fnActivityRecords[$surveyType];
        }
        $actId = 0;
        foreach ($surveyMap as $val) {
            if ($val['surveyType'] == $surveyType) {
                $actId = $val['surveyId'];
            }
        }
        if ($actId == 0) {
            self::$fnActivityRecords[$surveyType] = [];
            return self::$fnActivityRecords[$surveyType];
        }
        $studentUids       = self::getStudentUids();
        $fnActiveRecodeMap = (new Service_Data_FnActiveConfig())->getFnActiveList($studentUids, $actId, 1);
        if (empty($fnActiveRecodeMap)) {
            self::$fnActivityRecords[$surveyType] = [];
            return self::$fnActivityRecords[$surveyType];
        }
        self::$fnActivityRecords[$surveyType] = $fnActiveRecodeMap;
        return self::$fnActivityRecords[$surveyType];
    }

    public static function getRefundListMap() {
        $courseId = AssistantDesk_Data_CommonParams::$courseId;
        $lpcUid   = AssistantDesk_Data_CommonParams::$personUid;
        static $refundMap = null;
        if (($refundMap[$courseId][$lpcUid] ?? null) !== null) {
            return $refundMap[$courseId][$lpcUid];
        }
        $refundMap[$courseId][$lpcUid] = (new Service_Data_ScTrans())->getCourseUserRefundNum($courseId, $lpcUid);
        return $refundMap[$courseId][$lpcUid];
    }

    public static function getTransPvListMap() {
        $courseId = AssistantDesk_Data_CommonParams::$courseId;
        $lpcUid   = AssistantDesk_Data_CommonParams::$personUid;
        static $transMap = null;
        if (($transMap[$courseId][$lpcUid] ?? null) !== null) {
            return $transMap[$courseId][$lpcUid];
        }
        $transMap[$courseId][$lpcUid] = (new Service_Data_ScTrans())->getCourseUserTransPV($courseId, $lpcUid);
        return $transMap[$courseId][$lpcUid];
    }

    public static function getLeadsSliceScoreList() {
        if (self::$leadsSliceScoreList !== null) {
            return self::$leadsSliceScoreList;
        }
        $courseId            = AssistantDesk_Data_CommonParams::$courseId;
        $lpcUid              = AssistantDesk_Data_CommonParams::$personUid;
        $leadsIds            = self::getLeadsIds();
        $leadsSliceScoreList = Service_Data_Slice_Data::instance()->getLeadsList($courseId, $lpcUid, $leadsIds);
        if (empty($leadsSliceScoreList)) {
            $leadsSliceScoreList = [];
        }
        self::$leadsSliceScoreList = $leadsSliceScoreList;
        return self::$leadsSliceScoreList;
    }

    public static function getAllLeadsSliceScoreList() {
        if (self::$leadsSliceScoreListAll !== null) {
            return self::$leadsSliceScoreListAll;
        }
        $courseId            = AssistantDesk_Data_CommonParams::$courseId;
        $lpcUid              = AssistantDesk_Data_CommonParams::$personUid;
        $leadsIds            = self::getLeadsIds();
        $leadsSliceScoreList = Service_Data_Slice_Data::instance()->getAllLeadsList($courseId, $lpcUid, $leadsIds);
        if (empty($leadsSliceScoreList)) {
            $leadsSliceScoreList = [];
        }
        self::$leadsSliceScoreListAll = $leadsSliceScoreList;
        return self::$leadsSliceScoreListAll;
    }

    public static function leadsSliceTitles() {
        static $titleMapField = null;
        if ($titleMapField !== null) {
            return $titleMapField;
        }
        $courseInfo    = self::getCourseInfo();
        $titleMapField = [
            "分层总评级"   => "syntheticalShowScoreName",
            "教育意识评级"  => "educationRealizeScoreName",
            "学习表现评级"  => "learnShowScoreName",
            "购买力评级"   => "buyPowerScoreName",
            "配合度评级"   => "cooperateScoreName",
            "薄弱学科选项"  => "frailSubject",
            "补习班选项"   => "cramSchool",
            "网校认可度选项" => "onlineAccept",
        ];
        if (in_array($courseInfo['mainGradeId'], Service_Data_Slice_Data::getFurtherHigherSchool())) {
            $titleMapField['小升初规划选项'] = "furtherHigherSchool";
        }
        $titleMapField['出镜到课状态'] = "topicAppear";
        if ($courseInfo['mainSubjectId'] != Zb_Const_GradeSubject::ENGLISH) { // 英语不展示巩固练习
            $titleMapField['课后练习完成章节数'] = "afterExamFinish";
        }
        $titleMapField += [
            "历史购班课次数"   => "buyClassCourseNum",
            "历史购买短训班次数" => "buyShortCourseNum",
            "购买力评价"     => "buyPowerEstimate",
            "主动加微状态"    => "addWechatWish",
            "沟通意愿状态"    => "connectWish",
            "预约状态"      => "subscribeQuestion",
            "是否亲子共听状态"  => "parentTogether",
            "兴趣班"       => "interestClass",
            "未购买原因"     => "reasionNotBuyCourse",
            "是否接触过外教"   => "contactForeignTeacher",
            "决策人"       => "decisionMaker",
            "上课设备"      => "classroomEquipment",
            "数学分数"      => "subjectScore_2",
            "数学失分点"     => "courseLostFraction_2",
            "语文分数"      => "subjectScore_1",
            "语文失分点"     => "courseLostFraction_1",
            "英语分数"      => "subjectScore_3",
            "英语失分点'"    => "courseLostFraction_3",
        ];
        return $titleMapField;
    }

    public static function leadsSliceValues($leadsSliceInfo) {
        $courseInfo = self::getCourseInfo();
        $output     = [];
        if (empty($leadsSliceInfo)) {
            return $output;
        }
        $output['syntheticalShowScoreName']  = $leadsSliceInfo['syntheticalShowScoreName'];
        $output['educationRealizeScoreName'] = $leadsSliceInfo['educationRealizeScoreName'];
        $output['learnShowScoreName']        = $leadsSliceInfo['learnShowScoreName'];
        $output['buyPowerScoreName']         = $leadsSliceInfo['buyPowerScoreName'];
        $output['cooperateScoreName']        = $leadsSliceInfo['cooperateScoreName'];
        // 薄弱学科
        if (!empty($leadsSliceInfo['frailSubject'])) {
            foreach ($leadsSliceInfo['frailSubject'] as $subjectId) {
                if ($subjectId != Service_Data_Slice_Const::DEFINED_OPTION) {
                    $output['frailSubject'] .= '|' . Service_Data_Slice_Const::FRAIL_SUBJECT_OPTIONS[$subjectId];
                }
            }
            $output['frailSubject'] = trim($output['frailSubject'], '|');
        }

        //cramSchool 补习班
        $output['cramSchool'] = Service_Data_Slice_Const::CRAM_SCHOOL_OPTIONS[$leadsSliceInfo['cramSchool']];
        // 网络认可度
        $output['onlineAccept'] = Service_Data_Slice_Const::ONLINE_ACCEPT_OPTIONS[$leadsSliceInfo['onlineAccept']];
        //升学
        $output['furtherHigherSchool'] = Service_Data_Slice_Const::FURTHER_HIGHER_SCHOOL_OPTIONS[$leadsSliceInfo['furtherHigherSchool']] ?? '';
        // 出镜到课状态
        $output['topicAppear'] = Service_Data_Slice_Const::TOPIC_APPEAR_OPTIONS[$leadsSliceInfo['topicAppear']];
        //课后练习完成章节数
        $output['afterExamFinish'] = $leadsSliceInfo['afterExamFinish'];
        // 历史购班课次数 buyClassCourseNum
        $output['buyClassCourseNum'] = $leadsSliceInfo['buyClassCourseNum'];
        // 历史购买短训班次数
        $output['buyShortCourseNum'] = $leadsSliceInfo['buyShortCourseNum'];
        // 购买力评价
        $output['buyPowerEstimate'] = Service_Data_Slice_Const::BUY_POWER_ESTIMATE_OPTIONS[$leadsSliceInfo['buyPowerEstimate']];
        // 主动加微状态
        $output['buyPowerEstimate'] = Service_Data_Slice_Const::BUY_POWER_ESTIMATE_OPTIONS[$leadsSliceInfo['buyPowerEstimate']];
        // 沟通意愿状态
        $output['addWechatWish'] = Service_Data_Slice_Const::ADD_WECHAT_WISH_OPTIONS[$leadsSliceInfo['addWechatWish']];
        $output['connectWish']   = Service_Data_Slice_Const::CONNECT_WISH_OPTIONS[$leadsSliceInfo['connectWish']];
        // 预约状态
        $output['subscribeQuestion'] = Service_Data_Slice_Const::SUBSCRIBE_QUESTION_OPTIONS[$leadsSliceInfo['subscribeQuestion']];
        // 是否亲子共听状态
        $output['parentTogether'] = Service_Data_Slice_Const::PARENT_TOGETHER_OPTIONS[$leadsSliceInfo['parentTogether']];
        // 兴趣班
        $output['interestClass'] = '';
        foreach ($leadsSliceInfo['interestClass'] as $key) {
            if ($key != Service_Data_Slice_Const::DEFINED_OPTION) {
                $output['interestClass'] .= '|' . Service_Data_Slice_Const::INTEREST_CLASS_OPTIONS[$key];
            }
        }
        $output['interestClass'] = trim($output['interestClass'], '|');
        // 未购买原因
        $output['reasionNotBuyCourse'] = '';
        foreach ($leadsSliceInfo['reasionNotBuyCourse'] as $key) {
            if ($key != Service_Data_Slice_Const::DEFINED_OPTION) {
                $output['reasionNotBuyCourse'] .= '|' . Service_Data_Slice_Const::REASION_NOT_BUY_COURSE_OPTIONS[$key];
            }
        }
        $output['reasionNotBuyCourse'] = trim($output['reasionNotBuyCourse'], '|');
        // 是否接触过外教
        $output['contactForeignTeacher'] = Service_Data_Slice_Const::DEFINED_OPTION_MAP[$leadsSliceInfo['contactForeignTeacher']];
        // 决策人
        $output['decisionMaker'] = '';
        foreach ($leadsSliceInfo['decisionMaker'] as $key) {
            if ($key != Service_Data_Slice_Const::DEFINED_OPTION) {
                $output['decisionMaker'] .= '|' . Service_Data_Slice_Const::DEFINED_OPTION_MAP[$key];
            }
        }
        $output['decisionMaker'] = trim($output['decisionMaker'], '|');
        // 上课设备
        $output['classroomEquipment'] = '';
        foreach ($leadsSliceInfo['classroomEquipment'] as $key) {
            if ($key != Service_Data_Slice_Const::DEFINED_OPTION) {
                $output['classroomEquipment'] .= '|' . Service_Data_Slice_Const::DEFINED_OPTION_MAP[$key];
            }
        }
        $output['classroomEquipment'] = trim($output['classroomEquipment'], '|');
        // 数学分数
        $output['subjectScore_2']       = '';
        $output['courseLostFraction_2'] = '';
        // 数学失分点
        // 语文分数
        // 语文失分点
        $output['subjectScore_1']       = '';
        $output['courseLostFraction_1'] = '';
        // 英语分数
        // 英语失分点
        $output['subjectScore_3']       = '';
        $output['courseLostFraction_3'] = '';
        $subjectScore                   = json_decode(json_encode($leadsSliceInfo['subjectScore']), true);
        foreach ($subjectScore as $subjectId => $score) {
            $output['subjectScore_' . $subjectId] = Service_Data_Slice_Const::BASIC_SUBJECT_SCORE_OPTIONS[$score];
        }

        $courseLostFractionTips = [];
        foreach (Service_Data_Slice_Const::COURSE_LOST_FRACTION_OPTIONS as $key => $list) {
            foreach ($list as $k => $info) {
                foreach ($info as $code => $msg) {
                    $courseLostFractionTips[$code] = $msg;
                }
            }
        }
        foreach ($leadsSliceInfo['courseLostFraction'] as $subjectId => $list) {
            foreach ($list as $key) {
                if (!empty($courseLostFractionTips[$key])) {
                    $output['courseLostFraction_' . $subjectId] .= $courseLostFractionTips[$key] . '|';
                }
            }
        }
        return $output;
    }


    public static function getStudentCourseData() {
        if (self::$studentCourseData !== null) {
            return self::$studentCourseData;
        }
        $studentUids       = self::getStudentUids();
        $studentCourseData = (new Service_Data_StudentCourse())->getCourseInfoByStudentUids($studentUids);
        if (empty($studentCourseData)) {
            $studentCourseData = [];
        }
        self::$studentCourseData = $studentCourseData;
        return self::$studentCourseData;
    }

    public static function getVipRightsUserList() {
        if (self::$vipRightsUserList !== null) {
            return self::$vipRightsUserList;
        }
        $studentUids = self::getStudentUids();
        if (empty($studentUids)) {
            self::$vipRightsUserList = [];
            return self::$vipRightsUserList;
        }
        $courseId           = AssistantDesk_Data_CommonParams::$courseId;
        $courseMaterialInfo = Api_Tower::getCourseInfoList([$courseId], Duxuesc_Const::DEFAULT_SALE_MODE);
        $courseMaterialInfo = array_column($courseMaterialInfo, null, 'courseId');
        if (in_array($courseMaterialInfo[$courseId]['coursePriceTag'], [7, 27, 28, 29])) { //扩课 类型 获取用户vip信息
            $courseInfo = self::getCourseInfo();
            if (!empty($courseInfo['grades'])) {
                $vipCategory = 0;
                $grades      = $courseInfo['grades'];
                if (!empty(array_intersect($grades, Zb_Const_GradeSubject::$GRADEMAP[Zb_Const_GradeSubject::GRADE_STAGE_PRIMARY]))) { // 小学
                    $vipCategory = Api_Viprights_Const::PRIMARY_SCHOOL_MEMBER;
                } else if (!empty(array_intersect($grades, Zb_Const_GradeSubject::$GRADEMAP[Zb_Const_GradeSubject::GRADE_STAGE_JUNIOR]))) { // 初中
                    $vipCategory = Api_Viprights_Const::MIDDLE_SCHOOL_MEMBER;
                } else if (!empty(array_intersect($grades, Zb_Const_GradeSubject::$GRADEMAP[Zb_Const_GradeSubject::GRADE_STAGE_SENIOR]))) { // 高中
                    $vipCategory = Api_Viprights_Const::HIGH_SCHOOL_MEMBER;
                }

                if (empty($vipCategory)) {
                    self::$vipRightsUserList = [];
                    return self::$vipRightsUserList;
                }
                $res               = Api_Viprights::getUserVipInfo($studentUids, $vipCategory);
                $vipRightsUserList = [];
                foreach ($res as $info) {
                    foreach ($info['vipInfoList'] as $vipInfo) {
                        if ($vipInfo['vipCategory'] == $vipCategory) {
                            $vipRightsUserList[$info['userId']] = $vipInfo;
                        }
                    }
                }
                if (empty($vipRightsUserList)) {
                    $vipRightsUserList = [];
                }
                self::$vipRightsUserList = $vipRightsUserList;
                return self::$vipRightsUserList;
            }
        }
        self::$vipRightsUserList = [];
        return self::$vipRightsUserList;
    }

    public static function getStudentAnswerMap($cash = true) {
        if (self::$studentAnswerMap !== null && $cash) {
            return self::$studentAnswerMap;
        }
        $studentUids = self::getStudentUids();
        $surveyMap   = self::getSurveyMap($cash);
        if (empty($studentUids) || empty($surveyMap)) {
            self::$studentAnswerMap = [];
            return self::$studentAnswerMap;
        }
        $surveyId         = array_column($surveyMap, 'surveyId');
        $studentAnswerMap = (new Service_Data_Answer())->getShowAnswer(self::getLeadsStudentMap(), $surveyId, [1, 2], 1, [1, 2, 3, 4, 5, 6, 7]);
        if (empty($studentAnswerMap)) {
            $studentAnswerMap = [];
        }
        self::$studentAnswerMap = $studentAnswerMap;
        return self::$studentAnswerMap;
    }

    public static function getSurveyListTitleList($surveyType = 1) {
        static $surveyMap = [];
        if (($surveyMap[$surveyType] ?? null) !== null) {
            return $surveyMap[$surveyType];
        }
        $surveyId       = self::getCourseSurveyIdBySurveyType($surveyType);
        $surveyList     = self::getSurveyList();
        $needSurveyInfo = [];
        foreach ($surveyList as $key => $val) {
            if ($val['surveyType'] == $surveyType && $val['surveyId'] == $surveyId) {
                $needSurveyInfo = $val;
                break;
            }
        }
        $surveyMsg    = @json_decode($needSurveyInfo['surveyMsg'], true);
        $surveyMsg    = $surveyMsg ?? [];
        $surveyMsgArr = [];
        foreach ($surveyMsg as $value) {
            $surveyMsgArr[] = $value['title'];
        }
        $surveyMap[$surveyType] = $surveyMsgArr;
        return $surveyMap[$surveyType];
    }

    public static function getCourseSurveyIdBySurveyType($surveyType = 1) {
        static $typeMapId = [];
        if (($typeMapId[$surveyType] ?? null) !== null) {
            return $typeMapId[$surveyType];
        }
        $surveyMap = self::getSurveyMap();
        $surveyId  = 0;
        foreach ($surveyMap as $info) {
            if ($info['surveyType'] == $surveyType) {
                $surveyId = $info['surveyId'];
                break;
            }
        }
        $typeMapId[$surveyType] = $surveyId;
        return $typeMapId[$surveyType];
    }

    public static function getStudentSurveyAnswerList($surveyType = 1) {
        static $studentAnswerMap = [];
        if (($studentAnswerMap[$surveyType] ?? null) !== null) {
            return $studentAnswerMap[$surveyType];
        }
        $surveyId    = self::getCourseSurveyIdBySurveyType($surveyType);
        $studentUids = self::getStudentUids();
        if (empty($studentUids) || empty($surveyId)) {
            $studentAnswerMap[$surveyType] = [];
            return $studentAnswerMap[$surveyType];
        }
        $studentAnswer = (new Service_Data_Answer())->getAnswerList($studentUids, [$surveyId], $surveyType);
        if (empty($studentAnswer)) {
            $studentAnswerMap[$surveyType] = [];
            return $studentAnswerMap[$surveyType];
        }
        $studentAnswerLists = [];
        foreach ($studentAnswer as $v) {
            $studentAnswerLists[$v['studentUid']][] = $v;
        }
        $studentAnswerMap = [];
        foreach ($studentAnswerLists as $studentUid => $answerInfo) {
            foreach ($answerInfo as $item) {
                if ($item['questionId'] == 1) {
                    $studentAnswerMap[$studentUid][] = $item['answerMsg'];
                } else {
                    $studentAnswerMap[$studentUid][] = $item['optionValue'];
                }
            }
        }
        $studentAnswerMap[$surveyType] = $studentAnswerMap;
        return $studentAnswerMap[$surveyType];
    }

    public static function getStudentAnswerMapShowInfo($cash = true) {
        if (self::$studentAnswerMapShowInfo !== null && $cash) {
            return self::$studentAnswerMapShowInfo;
        }
        $studentAnswerMap               = self::getStudentAnswerMap($cash);
        self::$studentAnswerMapShowInfo = self::_showSurveyFiled($studentAnswerMap);
        return self::$studentAnswerMapShowInfo;
    }

    private static function _showSurveyFiled($studentAnswerMap) {
        if (empty($studentAnswerMap)) {
            return [];
        }

        foreach ($studentAnswerMap as $key => $val) {
            $needTag  = array_diff((array)$val[1]['tagId'], [1, 2, 3, 4]);
            $orderTag = array_diff((array)$val[2]['tagId'], [1, 2, 3, 4]);
            foreach ($needTag ?: [] as $val1) {
                unset($studentAnswerMap[$key][1]['tagId'][$val1], $studentAnswerMap[$key][1]['answer'][$val1], $studentAnswerMap[$key][1]['answerMsg'][$val1], $studentAnswerMap[$key][1]['questionType'][$val1]);
            }
            foreach ($orderTag ?: [] as $val2) {
                unset($studentAnswerMap[$key][2]['tagId'][$val2], $studentAnswerMap[$key][2]['answer'][$val2], $studentAnswerMap[$key][2]['answerMsg'][$val2], $studentAnswerMap[$key][2]['questionType'][$val2]);
            }
            if ($studentAnswerMap[$key][1]['answer']) {
                $studentAnswerMap[$key][1]['answer'] = array_values($studentAnswerMap[$key][1]['answer']);
            }
            if ($studentAnswerMap[$key][2]['answer']) {
                $studentAnswerMap[$key][2]['answer'] = array_values($studentAnswerMap[$key][2]['answer']);
            }

        }
        return $studentAnswerMap;
    }

    public static function getLastPlayBackTimeList() {
        $leadsIds         = self::getLeadsIds();
        $lessonIds        = self::getLessonIds();
        $lastPlayBackList = Common_Singleton::getInstanceData(AssistantDesk_Data_DataQuery::class, "getPlayBackInfo", [
            $lessonIds,
            $leadsIds,
            AssistantDesk_Data_CommonParams::$courseId,
        ]);
        if (empty($lastPlayBackList)) {
            $lastPlayBackList = [];
        }
        return $lastPlayBackList;
    }

    public static function getDbLeadsLessonsInfos() {
        if (self::$dbLeadsLessonsInfos !== null) {
            return self::$dbLeadsLessonsInfos;
        }
        $lpcUid    = AssistantDesk_Data_CommonParams::$personUid;
        $courseId  = AssistantDesk_Data_CommonParams::$courseId;
        $leadsIds  = self::getLeadsIds();
        $lessonIds = self::getLessonIds();
        $conds     = [
            'scUid'    => $lpcUid,
            'courseId' => $courseId,
            'lesson_id in (' . implode(',', $lessonIds) . ')',
            'leads_id in (' . implode(',', $leadsIds) . ')',
        ];
        $fields    = Dao_ScLeadsLesson::$ALL_FIELDS;
        $list      = (new Dao_ScLeadsLesson())->getListByConds($lpcUid, $conds, $fields);
        if (empty($list)) {
            $list = [];
        }
        self::$dbLeadsLessonsInfos = $list;
        return self::$dbLeadsLessonsInfos;
    }

    public static function getDbLeadsLessonInfos() {
        static $dbLeadsLessonInfos = [];
        $lpcUid   = AssistantDesk_Data_CommonParams::$personUid;
        $courseId = AssistantDesk_Data_CommonParams::$courseId;
        $lessonId = AssistantDesk_Data_CommonParams::$lessonId;
        if (isset($dbLeadsLessonInfos[$courseId][$lessonId][$lpcUid])) {
            return $dbLeadsLessonInfos[$courseId][$lessonId][$lpcUid];
        }
        $dbLeadsLessonInfos[$courseId][$lessonId][$lpcUid] = [];
        $leadsIds                                          = self::getLeadsIds();
        $conds                                             = [
            'scUid'    => $lpcUid,
            'courseId' => $courseId,
            'lessonId' => $lessonId,
            'leads_id in (' . implode(',', $leadsIds) . ')',
        ];
        $fields                                            = Dao_ScLeadsLesson::$ALL_FIELDS;
        $list                                              = (new Dao_ScLeadsLesson())->getListByConds($lpcUid, $conds, $fields);
        if (empty($list)) {
            $list = [];
        }
        $dbLeadsLessonInfos[$courseId][$lessonId][$lpcUid] = $list;
        return $dbLeadsLessonInfos[$courseId][$lessonId][$lpcUid];
    }

    /**
     * @return array|mixed|null
     */
    public static function getLessonStrengthPractice() {
        return self::callOnce(__METHOD__ . AssistantDesk_Data_CommonParams::$lessonId, function () {
            $lessonId                  = AssistantDesk_Data_CommonParams::$lessonId;
            $tmpLessonStrengthPractice = Api_Examcore::batchGetLessonExams([$lessonId], Duxuesc_Const::BIND_TYPE_PRACTICE_STRENGTH);
            $lessonStrengthPractice    = $tmpLessonStrengthPractice[$lessonId] ?? [];
            if (empty($lessonStrengthPractice)) {
                $courseInfo = self::getCourseInfo();
                $outlineId  = $courseInfo['lessonList'][$lessonId]['outlineId'] ?? 0;
                if (empty($outlineId)) {
                    return [];
                }
                $outlineStrengthPractice = (new Service_Data_Exam_ExamCore())->getExamInfoByBindId($outlineId, Api_Examcore::BUY_TYPE_OUTLINE, Api_Examcore::EXAM_TYPE_PRACTICE_STRENGTH);
                if (!empty($outlineStrengthPractice['exam']['examId'])) {
                    $lessonStrengthPractice = $outlineStrengthPractice['exam'];
                }
            }
            return $lessonStrengthPractice;
        });

    }

    public static function getStudentPerformanceMap() {
        return self::getLpcLeadsLessonEsData();
    }


    public static function getCourseExpiredTime() {
        if (self::$courseExpiredTime !== null) {
            return self::$courseExpiredTime;
        }
        $courseId    = AssistantDesk_Data_CommonParams::$courseId;
        if (Api_Mercury::CheckHitWithCourse(Api_Mercury::KEY_ASSISTANTDESK_INTERFACE_MIGRATION_GRAY, $courseId)) {
            $expiredList = self::diff($courseId);
        } else {
            $expiredList = Api_Lpcleads_Lpcleads::getExpiredTimeMapByCourseId([$courseId]);
        }
        
        if (empty($expiredList)) {
            self::$courseExpiredTime = 0;
            return self::$courseExpiredTime;
        }
        $expiredMap              = array_column($expiredList, null, 'courseId');
        self::$courseExpiredTime = $expiredMap[$courseId]['expireTime'];
        return self::$courseExpiredTime;
    }

    private static function diff($courseId){
        $expiredList = Api_Tower::getBatchExpireTimeByCourseList([$courseId]);
        $expiredMap = array_column($expiredList, null, 'courseId');
        $courseExpiredTime = $expiredMap[$courseId]['expireTime'];
        $expiredListOld = Api_Lpcleads_Lpcleads::getExpiredTimeMapByCourseId([$courseId]);
        $expiredMapOld = array_column($expiredListOld, null, 'courseId');
        $courseExpiredTimeOld = $expiredMapOld[$courseId]['expireTime'];
            
        if ($courseExpiredTime != $courseExpiredTimeOld) {
            Bd_Log::warning("courseExpiredTime diff: " . json_encode([
                'courseId' => $courseId,
                'expiredList' => $expiredList,
                'expiredListOld' => $expiredListOld,
            ]));
            $expiredList = $expiredListOld;
        }
        return $expiredList;
    }

    public static function getXlCourseTradeList() {
        if (self::$xlCourseTradeList !== null) {
            return self::$xlCourseTradeList;
        }
        $courseInfo      = self::getCourseInfo();
        $courseStartTime = $courseInfo['startTime'];
        $endTime         = self::getCourseExpiredTime();
        $studentUids     = self::getStudentUids();
        $lpckidList      = Api_Insight_Student::getXlCourseTradeByStudentUids($studentUids, $courseStartTime, $endTime);
        if (empty($lpckidList)) {
            $lpckidList = [];
        }
        self::$xlCourseTradeList = $lpckidList;
        return self::$xlCourseTradeList;
    }

    public static function getSurveyInfoMap() {
        if (self::$surveyInfoMap != null) {
            return self::$surveyInfoMap;
        }
        $surveyList = self::getSurveyList();
        $ret        = [
            'needPosterUrl'  => '',
            'orderPosterUrl' => ''
        ];
        if (empty($surveyList)) {
            self::$surveyInfoMap = $ret;
            return self::$surveyInfoMap;
        }
        foreach ($surveyList as $key => $val) {
            if ($val['surveyType'] == 1) {
                $ret['needPosterUrl'] = $val['posterUrl'];
            }
            if ($val['surveyType'] == 2) {
                $ret['orderPosterUrl'] = $val['posterUrl'];
            }
        }
        self::$surveyInfoMap = $ret;
        return self::$surveyInfoMap;
    }

    /**
     * $transList
     * @return array|false|null
     */
    public static function getTransList() {
        if (self::$transList !== null) {
            return self::$transList;
        }
        $courseId  = AssistantDesk_Data_CommonParams::$courseId;
        $lpcUid    = AssistantDesk_Data_CommonParams::$personUid;
        $leadsIds  = self::getLeadsIds();
        $transList = (new Service_Data_ScTrans())->getTransList($lpcUid, $courseId, $leadsIds);
        if (empty($transList)) {
            $transList = [];
        }
        $transListMap = [];
        foreach ($transList as $_trans) {
            $transListMap[$_trans['leadsId']][] = $_trans;
        }
        self::$transList = $transListMap;
        return self::$transList;
    }

    /**
     * $xlbcReportUrl
     * @return array|null
     */
    public static function getXlbcReportUrl() {
        return self::callOnce(__METHOD__ . AssistantDesk_Data_CommonParams::$lessonId, function () {
            $lessonId    = AssistantDesk_Data_CommonParams::$lessonId;
            $studentUids = self::getStudentUids();
            $reportUrls  = Api_Stallman_Api::getStudyReport($lessonId, $studentUids);
            if (empty($reportUrls)) {
                $reportUrls = [];
            }
            return $reportUrls;
        });

    }

    /**
     * $xlbcCorrectStatus
     * @return array|null
     */
    public static function getXlbcCorrectStatus() {
        return self::callOnce(__METHOD__ . AssistantDesk_Data_CommonParams::$lessonId, function () {
            $lessonId      = AssistantDesk_Data_CommonParams::$lessonId;
            $studentUids   = self::getStudentUids();
            $correctStatus = Api_Stallman_Api::getRecorrectStatus($lessonId, $studentUids);
            if (empty($correctStatus)) {
                $correctStatus = [];
            }
            return $correctStatus;
        });
    }

    public static function getStudentCoupons() {
        if (self::$studentCoupons !== null) {
            return self::$studentCoupons;
        }
        $studentUids    = self::getStudentUids();
        $studentCoupons = (new Service_Data_StudentCoupon())->getCouponInfoByStudentUids($studentUids);
        if (empty($studentCoupons)) {
            $studentCoupons = [];
        }
        self::$studentCoupons = $studentCoupons;
        return self::$studentCoupons;
    }

    public static function getLeadsExtInfo() {
        if (self::$getLeadsExtInfo !== null) {
            return self::$getLeadsExtInfo;
        }
        $courseId        = AssistantDesk_Data_CommonParams::$courseId;
        $lpcUid          = AssistantDesk_Data_CommonParams::$personUid;
        $getLeadsExtInfo = (new Service_Data_LessonStudentExtInfo())->getListByLpcUidCid($lpcUid, $courseId);
        if (empty($getLeadsExtInfo)) {
            $getLeadsExtInfo = [];
        }
        //格式化
        self::$getLeadsExtInfo = [];
        foreach ($getLeadsExtInfo as $ext) {
            self::$getLeadsExtInfo[$ext['leadsId']][$ext['lessonId']] = $ext;
        }
        return self::$getLeadsExtInfo;
    }
}